{% extends "base.html" %}

{% block title %}EXIF分析 - 照片分析器{% endblock %}

{% block content %}
<div class="page-header">
    <h1>🔍 EXIF数据分析</h1>
    <p>基于照片EXIF数据的智能分析，了解您的拍摄习惯和技术特征</p>
</div>

{% if not has_data %}
<div class="alert alert-warning">
    <h3>⚠️ 没有可用的数据</h3>
    <p>请先在<a href="/settings">设置页面</a>添加照片路径并进行分析，然后回到此页面进行EXIF分析。</p>
</div>
{% else %}
<div class="stats-bar">
    <div class="stat-item">
        <span class="stat-number">{{ total_photos }}</span>
        <span class="stat-label">总照片数</span>
    </div>
</div>

<div class="analysis-container">
    <!-- 分析类型选择 -->
    <div class="analysis-selector">
        <h3>选择分析类型</h3>
        <div class="analysis-grid">
            <div class="analysis-card" data-type="focal_length">
                <div class="card-icon">📏</div>
                <h4>焦段分析</h4>
                <p>分析焦段使用习惯和偏好</p>
            </div>
            <div class="analysis-card" data-type="aperture">
                <div class="card-icon">🔍</div>
                <h4>光圈分析</h4>
                <p>分析光圈使用习惯和景深偏好</p>
            </div>
            <div class="analysis-card" data-type="shutter_speed">
                <div class="card-icon">⚡</div>
                <h4>快门速度分析</h4>
                <p>分析快门速度使用和稳定性</p>
            </div>
            <div class="analysis-card" data-type="iso">
                <div class="card-icon">🌙</div>
                <h4>ISO分析</h4>
                <p>分析ISO使用习惯和低光性能</p>
            </div>
            <div class="analysis-card" data-type="camera_body">
                <div class="card-icon">📷</div>
                <h4>机身分析</h4>
                <p>分析相机机身使用情况</p>
            </div>
            <div class="analysis-card" data-type="lens">
                <div class="card-icon">🔭</div>
                <h4>镜头分析</h4>
                <p>分析镜头使用习惯和偏好</p>
            </div>
            <div class="analysis-card" data-type="user_preference">
                <div class="card-icon">👤</div>
                <h4>用户喜好分析</h4>
                <p>分析拍摄风格和个人偏好</p>
            </div>
            <div class="analysis-card" data-type="hand_stability">
                <div class="card-icon">🤚</div>
                <h4>手持稳定性分析</h4>
                <p>分析手持拍摄稳定性水平</p>
            </div>
            <div class="analysis-card" data-type="comprehensive">
                <div class="card-icon">📊</div>
                <h4>综合分析</h4>
                <p>全面的摄影师画像分析</p>
            </div>
        </div>
    </div>

    <!-- 批量分析按钮 -->
    <div class="batch-analysis-section">
        <button id="batch-analysis-btn" class="btn btn-primary">
            <span>🚀</span> 执行所有分析
        </button>
        <p>一键执行所有类型的EXIF分析</p>
    </div>

    <!-- 自定义指令 -->
    <div class="custom-instruction-section">
        <h3>自定义指令</h3>
        <textarea id="custom-instruction" placeholder="在这里输入您的自定义分析指令（可选）..."></textarea>
    </div>

    <!-- 分析结果 -->
    <div id="analysis-results" class="analysis-results" style="display: none;">
        <h3>分析结果</h3>
        <div id="results-content"></div>
    </div>
</div>

<!-- 加载遮罩 -->
<div id="loading-overlay" class="loading-overlay" style="display: none;">
    <div class="loading-spinner"></div>
    <p>正在进行AI分析，请稍候...</p>
</div>
{% endif %}

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const analysisCards = document.querySelectorAll('.analysis-card');
    const batchAnalysisBtn = document.getElementById('batch-analysis-btn');
    const customInstructionInput = document.getElementById('custom-instruction');
    const loadingOverlay = document.getElementById('loading-overlay');
    const resultsSection = document.getElementById('analysis-results');
    const resultsContent = document.getElementById('results-content');

    // 单个分析卡片点击事件
    analysisCards.forEach(card => {
        card.addEventListener('click', function() {
            const analysisType = this.dataset.type;
            const customInstruction = customInstructionInput.value.trim();
            performAnalysis(analysisType, customInstruction);
        });
    });

    // 批量分析按钮点击事件
    batchAnalysisBtn.addEventListener('click', function() {
        performBatchAnalysis();
    });

    // 执行单个分析
    function performAnalysis(analysisType, customInstruction = '') {
        showLoading();
        
        const formData = new FormData();
        formData.append('analysis_type', analysisType);
        formData.append('custom_instruction', customInstruction);

        fetch('/api/exif-analysis', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                displaySingleResult(data.analysis_type, data.result);
            } else {
                showError('分析失败: ' + (data.detail || '未知错误'));
            }
        })
        .catch(error => {
            hideLoading();
            showError('分析失败: ' + error.message);
        });
    }

    // 执行批量分析
    function performBatchAnalysis() {
        showLoading();
        
        fetch('/api/exif-analysis-batch')
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                displayBatchResults(data.results);
            } else {
                showError('批量分析失败: ' + (data.detail || '未知错误'));
            }
        })
        .catch(error => {
            hideLoading();
            showError('批量分析失败: ' + error.message);
        });
    }

    // 显示单个分析结果
    function displaySingleResult(analysisType, result) {
        const analysisTypeNames = {
            'focal_length': '焦段分析',
            'aperture': '光圈分析',
            'shutter_speed': '快门速度分析',
            'iso': 'ISO分析',
            'camera_body': '机身分析',
            'lens': '镜头分析',
            'user_preference': '用户喜好分析',
            'hand_stability': '手持稳定性分析',
            'comprehensive': '综合分析'
        };

        const resultHtml = `
            <div class="result-section">
                <h4>${analysisTypeNames[analysisType]}</h4>
                <div class="result-content">
                    ${formatResult(result)}
                </div>
            </div>
        `;

        resultsContent.innerHTML = resultHtml;
        resultsSection.style.display = 'block';
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    // 显示批量分析结果
    function displayBatchResults(results) {
        let resultHtml = '<div class="batch-results">';
        
        for (const [analysisType, data] of Object.entries(results)) {
            if (data.success) {
                resultHtml += `
                    <div class="result-section">
                        <h4>${data.description}</h4>
                        <div class="result-content">
                            ${formatResult(data.result)}
                        </div>
                    </div>
                `;
            } else {
                resultHtml += `
                    <div class="result-section error">
                        <h4>${data.description}</h4>
                        <div class="error-message">
                            分析失败: ${data.error}
                        </div>
                    </div>
                `;
            }
        }
        
        resultHtml += '</div>';
        resultsContent.innerHTML = resultHtml;
        resultsSection.style.display = 'block';
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    // 格式化分析结果
    function formatResult(result) {
        let html = '<div class="formatted-result">';
        
        for (const [key, value] of Object.entries(result)) {
            const formattedKey = formatKey(key);
            const formattedValue = formatValue(value);
            
            html += `
                <div class="result-item">
                    <strong>${formattedKey}:</strong>
                    <span>${formattedValue}</span>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }

    // 格式化键名
    function formatKey(key) {
        const keyMap = {
            'most_used_focal_length': '最常用焦段',
            'most_used_aperture': '最常用光圈',
            'most_used_shutter_speed': '最常用快门速度',
            'most_used_iso': '最常用ISO',
            'primary_camera': '主要相机',
            'most_used_lens': '最常用镜头',
            'overall_stability': '整体稳定性',
            'photographer_profile': '摄影师画像',
            'recommendations': '建议',
            'strengths': '优势',
            'weaknesses': '不足',
            'improvement_suggestions': '改进建议'
        };
        
        return keyMap[key] || key.replace(/_/g, ' ');
    }

    // 格式化值
    function formatValue(value) {
        if (Array.isArray(value)) {
            return value.join(', ');
        } else if (typeof value === 'object' && value !== null) {
            let html = '<ul>';
            for (const [k, v] of Object.entries(value)) {
                html += `<li><strong>${k}:</strong> ${v}</li>`;
            }
            html += '</ul>';
            return html;
        }
        return value;
    }

    // 显示加载动画
    function showLoading() {
        loadingOverlay.style.display = 'flex';
    }

    // 隐藏加载动画
    function hideLoading() {
        loadingOverlay.style.display = 'none';
    }

    // 显示错误信息
    function showError(message) {
        resultsContent.innerHTML = `
            <div class="error-message">
                <h4>❌ 错误</h4>
                <p>${message}</p>
            </div>
        `;
        resultsSection.style.display = 'block';
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }
});
</script>
{% endblock %}
