# EXIF数据分析功能

## 功能概述

本项目新增了基于EXIF数据的智能分析功能，能够通过分析照片的拍摄参数来了解用户的拍摄习惯、技术水平和个人偏好。

## 分析功能

### 1. 焦段分析 (Focal Length Analysis)
- **功能**: 分析焦段使用习惯和偏好
- **包含内容**:
  - 最常用焦段
  - 焦段分布统计
  - 偏好焦段范围
  - 拍摄风格推测
  - 广角/标准/长焦使用比例
  - 焦段使用建议

### 2. 光圈分析 (Aperture Analysis)
- **功能**: 分析光圈使用习惯和景深偏好
- **包含内容**:
  - 最常用光圈
  - 光圈分布统计
  - 平均光圈值
  - 景深偏好
  - 低光性能评估
  - 人像/风景光圈使用比例
  - 光圈使用建议

### 3. 快门速度分析 (Shutter Speed Analysis)
- **功能**: 分析快门速度使用和稳定性
- **包含内容**:
  - 最常用快门速度
  - 快门速度分布统计
  - 平均快门速度
  - 手持拍摄能力评估
  - 运动模糊风险评估
  - 高速/慢速快门使用比例
  - 快门速度使用建议

### 4. ISO分析 (ISO Analysis)
- **功能**: 分析ISO使用习惯和低光性能
- **包含内容**:
  - 最常用ISO
  - ISO分布统计
  - 平均ISO值
  - 噪点容忍度评估
  - 低光拍摄频率
  - 日光拍摄比例
  - 高ISO使用信心
  - ISO使用建议

### 5. 机身分析 (Camera Body Analysis)
- **功能**: 分析相机机身使用情况
- **包含内容**:
  - 主要使用机身
  - 机身使用分布
  - 相机类型
  - 拍摄量统计
  - 使用一致性评分
  - 升级建议

### 6. 镜头分析 (Lens Analysis)
- **功能**: 分析镜头使用习惯和偏好
- **包含内容**:
  - 最常用镜头
  - 镜头使用分布
  - 镜头类型偏好
  - 焦段覆盖范围
  - 定焦vs变焦比例
  - 专业化程度
  - 镜头使用建议

### 7. 用户喜好分析 (User Preference Analysis)
- **功能**: 分析拍摄风格和个人偏好
- **包含内容**:
  - 拍摄风格偏好
  - 拍摄主题偏好
  - 技术水平评估
  - 冒险倾向
  - 器材使用模式
  - 拍摄频率
  - 时间偏好
  - 创造力评分
  - 改进方向

### 8. 手持稳定性分析 (Hand Stability Analysis)
- **功能**: 分析手持拍摄稳定性水平
- **包含内容**:
  - 整体稳定性
  - 不同焦段稳定性
  - 安全快门遵守率
  - 风险拍摄比例
  - 防抖依赖程度
  - 稳定性改进建议
  - 三脚架使用建议

### 9. 综合分析 (Comprehensive Analysis)
- **功能**: 全面的摄影师画像分析
- **包含内容**:
  - 摄影师画像
  - 技能水平评估
  - 器材优化评分
  - 拍摄效率评分
  - 技术一致性评分
  - 创意探索评分
  - 优势分析
  - 不足分析
  - 下一步建议

## 使用方法

### 1. Web界面使用
1. 确保已添加照片路径并完成基础分析
2. 访问 `http://localhost:8000/exif-analysis`
3. 选择要进行的分析类型
4. 可选择添加自定义指令
5. 点击分析卡片或批量分析按钮
6. 查看分析结果

### 2. 编程接口使用

#### 便捷函数
```python
from modules.llm_api import analyze_exif_data

# 分析焦段使用习惯
result = analyze_exif_data("data/raw.csv", "focal_length")
print(f"最常用焦段: {result.most_used_focal_length}")

# 使用自定义指令
custom_instruction = "请特别关注夜景拍摄情况"
result = analyze_exif_data("data/raw.csv", "iso", custom_instruction)
```

#### 分析器类
```python
from modules.llm_api import EXIFAnalyzer

analyzer = EXIFAnalyzer()

# 分析光圈使用习惯
aperture_result = analyzer.analyze_aperture("data/raw.csv")

# 分析手持稳定性
stability_result = analyzer.analyze_hand_stability("data/raw.csv")

# 综合分析
comprehensive_result = analyzer.analyze_comprehensive("data/raw.csv")
```

### 3. API接口使用

#### 单个分析
```bash
curl -X POST "http://localhost:8000/api/exif-analysis" \
  -F "analysis_type=focal_length" \
  -F "custom_instruction=请关注广角使用情况"
```

#### 批量分析
```bash
curl -X GET "http://localhost:8000/api/exif-analysis-batch"
```

## 技术实现

### 数据处理
- 使用pandas读取CSV格式的EXIF数据
- 根据分析类型筛选相关列
- 智能数据预处理和过滤

### AI分析
- 使用Google Gemini模型进行智能分析
- 结构化输出，确保数据一致性
- 支持自定义指令，满足个性化需求

### 前端展示
- 响应式设计，支持移动端
- 直观的卡片式界面
- 实时分析结果显示
- 支持批量分析和单个分析

## 配置要求

### 环境配置
- Python 3.8+
- Google Gemini API密钥
- 依赖包：pandas, google-genai, pydantic

### 数据格式
CSV文件应包含以下字段：
- `文件名`: 照片文件名
- `拍摄设备`: 相机机身型号
- `镜头`: 镜头型号
- `光圈`: 光圈值 (如: f/2.8)
- `快门`: 快门速度 (如: 1/125s)
- `ISO`: ISO值
- `等效35mm焦距`: 等效35mm焦距
- `拍摄日期`: 拍摄日期
- `时间`: 拍摄时间

## 示例运行

运行示例脚本：
```bash
python example_exif_analysis.py
```

或者运行测试脚本：
```bash
python test_exif_analysis.py
```

## 注意事项

1. **API密钥**: 需要配置Google Gemini API密钥
2. **数据质量**: 分析结果依赖于EXIF数据的完整性
3. **网络连接**: 需要稳定的网络连接调用AI服务
4. **处理时间**: 复杂分析可能需要较长时间
5. **结果准确性**: AI分析结果仅供参考，具体情况需要结合实际使用

## 扩展功能

可以基于现有框架添加更多分析类型：
- 时间分析（拍摄时间偏好）
- 地理位置分析（拍摄地点偏好）
- 设备升级建议
- 摄影技巧推荐
- 个性化学习计划

## 技术支持

如有问题或建议，请查看：
- 错误日志
- API文档
- 示例代码
- 测试脚本
