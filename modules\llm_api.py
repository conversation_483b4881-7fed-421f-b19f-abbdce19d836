"""
LLM API 模块
使用 Google Gemini 实现图片分析功能
"""

import os
import io
import time
import pandas as pd
from typing import Optional, Type, Union
from pydantic import BaseModel

# 导入配置
try:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from llm_config import GOOGLE_API_KEY, DEFAULT_MODEL, GENERATION_CONFIG
    DEFAULT_API_KEY = GOOGLE_API_KEY
    DEFAULT_MODEL_NAME = DEFAULT_MODEL
    DEFAULT_GENERATION_CONFIG = GENERATION_CONFIG
except ImportError:
    DEFAULT_API_KEY = None
    DEFAULT_MODEL_NAME = "gemini-2.5-flash"
    DEFAULT_GENERATION_CONFIG = {
        "temperature": 0.7,
        "top_p": 0.95,
        "top_k": 40,
        "max_output_tokens": 8192,
    }

# 导入 Google Gemini API
try:
    from google import genai
except ImportError:
    genai = None
    print("警告: 未安装 google-genai 库，请运行: pip install google-genai")

# 导入 schema 和 prompts
from .schema import (
    PhotoAnalysisResult, QuickAnalysisResult, FocalLengthAnalysis, 
    ApertureAnalysis, ShutterSpeedAnalysis, ISOAnalysis, CameraBodyAnalysis,
    LensAnalysis, UserPreferenceAnalysis, HandStabilityAnalysis, 
    ComprehensiveAnalysis
)
from .prompts import get_prompt_by_type


class LLMError(Exception):
    """LLM相关错误"""
    pass


class LLMClient:
    """LLM客户端，使用Google Gemini API"""
    
    def __init__(self, api_key: Optional[str] = None, model_name: Optional[str] = None):
        """初始化LLM客户端"""
        if genai is None:
            raise LLMError("Google Gemini API 未安装，请运行: pip install google-genai")
        
        # 获取API密钥
        self.api_key = api_key or DEFAULT_API_KEY or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise LLMError("未找到 Google API 密钥")
        
        # 获取模型名称
        self.model_name = model_name or DEFAULT_MODEL_NAME
        self.client = genai.Client(api_key=self.api_key)
        self.generation_config = DEFAULT_GENERATION_CONFIG
    
    def _prepare_image_content(self, image_path: str):
        """准备图片内容"""
        try:
            from PIL import Image
            
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 压缩图片
                max_size = (1024, 1024)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # 转换为字节流
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                image_bytes = buffer.getvalue()
                
                return {
                    "inline_data": {
                        "mime_type": "image/jpeg",
                        "data": image_bytes
                    }
                }
        except Exception as e:
            raise LLMError(f"处理图片失败: {str(e)}")
    
    def _prepare_csv_content(self, csv_data: str, analysis_type: str) -> str:
        """准备CSV数据内容"""
        try:
            # 根据分析类型选择相关的列
            column_mapping = {
                "focal_length": ["等效35mm焦距", "拍摄设备", "镜头"],
                "aperture": ["光圈", "拍摄设备", "镜头", "等效35mm焦距"],
                "shutter_speed": ["快门", "等效35mm焦距", "拍摄设备", "ISO"],
                "iso": ["ISO", "拍摄设备", "快门", "光圈"],
                "camera_body": ["拍摄设备", "拍摄日期", "时间"],
                "lens": ["镜头", "等效35mm焦距", "拍摄设备"],
                "user_preference": ["拍摄设备", "镜头", "光圈", "快门", "ISO", "等效35mm焦距", "拍摄日期", "时间"],
                "hand_stability": ["等效35mm焦距", "快门", "拍摄设备"],
                "comprehensive": ["文件名", "拍摄设备", "镜头", "光圈", "快门", "ISO", "等效35mm焦距", "拍摄日期", "时间"]
            }
            
            if analysis_type not in column_mapping:
                return csv_data
            
            # 解析CSV数据
            lines = csv_data.strip().split('\n')
            if len(lines) < 2:
                return csv_data
                
            headers = lines[0].split(',')
            relevant_columns = column_mapping[analysis_type]
            
            # 找到相关列的索引
            relevant_indices = []
            for col in relevant_columns:
                try:
                    idx = headers.index(col)
                    relevant_indices.append(idx)
                except ValueError:
                    continue
            
            if not relevant_indices:
                return csv_data
            
            # 提取相关数据
            filtered_lines = []
            filtered_headers = [headers[i] for i in relevant_indices]
            filtered_lines.append(','.join(filtered_headers))
            
            for line in lines[1:]:
                row = line.split(',')
                if len(row) > max(relevant_indices):
                    filtered_row = [row[i] for i in relevant_indices]
                    filtered_lines.append(','.join(filtered_row))
            
            return '\n'.join(filtered_lines)
            
        except Exception as e:
            print(f"CSV数据处理警告: {str(e)}")
            return csv_data
    
    def generate_response(self, prompt: str, schema_class: Type[BaseModel], 
                         image_path: Optional[str] = None, 
                         csv_data: Optional[str] = None,
                         analysis_type: str = "basic") -> BaseModel:
        """生成结构化响应"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 准备内容
                content_parts = [prompt]
                
                # 添加图片内容
                if image_path and os.path.exists(image_path):
                    image_data = self._prepare_image_content(image_path)
                    content_parts.append(image_data)
                
                # 添加CSV数据
                if csv_data:
                    processed_csv = self._prepare_csv_content(csv_data, analysis_type)
                    content_parts.append(f"\n\n以下是EXIF数据（CSV格式）：\n{processed_csv}")
                
                # 调用 API
                response = self.client.models.generate_content(
                    model=self.model_name,
                    contents=content_parts,
                    config={
                        "response_mime_type": "application/json",
                        "response_schema": schema_class,
                        **self.generation_config
                    }
                )
                
                return response.parsed
                
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"尝试 {attempt + 1}/{max_retries} 失败，重试中...")
                    time.sleep(1)
                else:
                    raise LLMError(f"生成响应失败: {str(e)}")


class PhotoAnalyzer:
    """图片分析器"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """初始化图片分析器"""
        self.llm_client = llm_client or LLMClient()
    
    def analyze_photo(self, image_path: str, analysis_type: str = "quick", 
                     custom_instruction: str = "") -> Union[PhotoAnalysisResult, QuickAnalysisResult]:
        """分析单张图片"""
        prompt = get_prompt_by_type(analysis_type, custom_instruction)
        
        # 选择schema
        if analysis_type == "quick":
            schema_class = QuickAnalysisResult
        else:
            schema_class = PhotoAnalysisResult
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=schema_class,
            image_path=image_path,
            analysis_type=analysis_type
        )


class EXIFAnalyzer:
    """EXIF数据分析器"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """初始化EXIF分析器"""
        self.llm_client = llm_client or LLMClient()
    
    def _load_csv_data(self, csv_path: str) -> str:
        """加载CSV数据"""
        try:
            df = pd.read_csv(csv_path)
            return df.to_csv(index=False)
        except Exception as e:
            raise LLMError(f"加载CSV文件失败: {str(e)}")
    
    def analyze_focal_length(self, csv_path: str, custom_instruction: str = "") -> FocalLengthAnalysis:
        """分析焦段使用习惯"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("focal_length", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=FocalLengthAnalysis,
            csv_data=csv_data,
            analysis_type="focal_length"
        )
    
    def analyze_aperture(self, csv_path: str, custom_instruction: str = "") -> ApertureAnalysis:
        """分析光圈使用习惯"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("aperture", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=ApertureAnalysis,
            csv_data=csv_data,
            analysis_type="aperture"
        )
    
    def analyze_shutter_speed(self, csv_path: str, custom_instruction: str = "") -> ShutterSpeedAnalysis:
        """分析快门速度使用习惯"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("shutter_speed", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=ShutterSpeedAnalysis,
            csv_data=csv_data,
            analysis_type="shutter_speed"
        )
    
    def analyze_iso(self, csv_path: str, custom_instruction: str = "") -> ISOAnalysis:
        """分析ISO使用习惯"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("iso", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=ISOAnalysis,
            csv_data=csv_data,
            analysis_type="iso"
        )
    
    def analyze_camera_body(self, csv_path: str, custom_instruction: str = "") -> CameraBodyAnalysis:
        """分析机身使用情况"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("camera_body", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=CameraBodyAnalysis,
            csv_data=csv_data,
            analysis_type="camera_body"
        )
    
    def analyze_lens(self, csv_path: str, custom_instruction: str = "") -> LensAnalysis:
        """分析镜头使用习惯"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("lens", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=LensAnalysis,
            csv_data=csv_data,
            analysis_type="lens"
        )
    
    def analyze_user_preference(self, csv_path: str, custom_instruction: str = "") -> UserPreferenceAnalysis:
        """分析用户喜好"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("user_preference", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=UserPreferenceAnalysis,
            csv_data=csv_data,
            analysis_type="user_preference"
        )
    
    def analyze_hand_stability(self, csv_path: str, custom_instruction: str = "") -> HandStabilityAnalysis:
        """分析手持稳定性"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("hand_stability", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=HandStabilityAnalysis,
            csv_data=csv_data,
            analysis_type="hand_stability"
        )
    
    def analyze_comprehensive(self, csv_path: str, custom_instruction: str = "") -> ComprehensiveAnalysis:
        """综合分析"""
        csv_data = self._load_csv_data(csv_path)
        prompt = get_prompt_by_type("comprehensive", custom_instruction)
        
        return self.llm_client.generate_response(
            prompt=prompt,
            schema_class=ComprehensiveAnalysis,
            csv_data=csv_data,
            analysis_type="comprehensive"
        )


# 便捷函数
def analyze_single_photo(image_path: str, analysis_type: str = "quick", 
                        custom_instruction: str = "") -> Union[QuickAnalysisResult, PhotoAnalysisResult]:
    """分析单张图片的便捷函数"""
    analyzer = PhotoAnalyzer()
    return analyzer.analyze_photo(image_path, analysis_type, custom_instruction)

def analyze_exif_data(csv_path: str, analysis_type: str, 
                     custom_instruction: str = "") -> BaseModel:
    """分析EXIF数据的便捷函数"""
    analyzer = EXIFAnalyzer()
    
    analysis_methods = {
        "focal_length": analyzer.analyze_focal_length,
        "aperture": analyzer.analyze_aperture,
        "shutter_speed": analyzer.analyze_shutter_speed,
        "iso": analyzer.analyze_iso,
        "camera_body": analyzer.analyze_camera_body,
        "lens": analyzer.analyze_lens,
        "user_preference": analyzer.analyze_user_preference,
        "hand_stability": analyzer.analyze_hand_stability,
        "comprehensive": analyzer.analyze_comprehensive,
    }
    
    if analysis_type not in analysis_methods:
        raise LLMError(f"不支持的分析类型: {analysis_type}")
    
    return analysis_methods[analysis_type](csv_path, custom_instruction)




