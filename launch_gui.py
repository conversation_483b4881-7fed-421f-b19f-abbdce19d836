#!/usr/bin/env python3
"""
照片分析器 GUI 启动脚本
美化版，更好的用户体验
"""

import os
import sys
import webbrowser
import threading
import time
import uvicorn
import platform
from app import app
from modules.utils import Colors

def print_banner():
    """打印美化的启动横幅"""
    banner = f"""
{Colors.BLUE}{Colors.BOLD}┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃                                                 ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃  {Colors.GREEN}📊 Photo Analyzer - 照片分析工具{Colors.BLUE}             ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃                                                 ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃  {Colors.YELLOW}版本: 1.0.0{Colors.BLUE}                                ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃  {Colors.YELLOW}日期: 2025年7月{Colors.BLUE}                            ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┃                                                 ┃{Colors.END}
{Colors.BLUE}{Colors.BOLD}┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛{Colors.END}
    """
    print(banner)

def open_browser():
    """在浏览器中打开应用"""
    time.sleep(1.5)  # 等待服务器完全启动
    url = 'http://127.0.0.1:8000'
    webbrowser.open(url)
    print(f"{Colors.GREEN}✓ 已在默认浏览器中打开应用界面: {url}{Colors.END}")

def check_environment():
    """检查运行环境"""
    print(f"{Colors.YELLOW}⚙️  环境检查中...{Colors.END}")
    
    # 检查操作系统
    os_name = platform.system()
    os_version = platform.version()
    print(f"  • 操作系统: {os_name} {os_version}")
    
    # 检查Python版本
    py_version = platform.python_version()
    print(f"  • Python版本: {py_version}")
    
    # 检查数据目录
    if not os.path.exists('./data'):
        print(f"{Colors.YELLOW}  • 数据目录不存在，正在创建...{Colors.END}")
        os.makedirs('./data', exist_ok=True)
        print(f"{Colors.GREEN}    ✓ 数据目录已创建{Colors.END}")
    else:
        print(f"{Colors.GREEN}  • 数据目录已存在{Colors.END}")
    
    # 检查配置文件
    if os.path.exists('./config.json'):
        print(f"{Colors.GREEN}  • 配置文件已存在{Colors.END}")
    else:
        print(f"{Colors.YELLOW}  • 配置文件不存在，将在首次运行时创建{Colors.END}")
    
    print(f"{Colors.GREEN}✓ 环境检查完成{Colors.END}")

if __name__ == "__main__":
    # 清屏（跨平台）
    os.system('cls' if os.name == 'nt' else 'clear')
    
    # 打印横幅
    print_banner()
    
    # 检查环境
    check_environment()
    
    # 启动信息
    print(f"\n{Colors.BLUE}🚀 正在启动照片分析器...{Colors.END}")
    
    # 自动打开浏览器
    threading.Thread(target=open_browser).start()
    
    # 运行服务器
    print(f"{Colors.YELLOW}⚡ 启动Web服务器...{Colors.END}")
    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            reload=False,
            access_log=False
        )
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}👋 照片分析器已关闭，感谢使用！{Colors.END}")
    except Exception as e:
        print(f"\n{Colors.RED}❌ 启动服务器时出错: {str(e)}{Colors.END}")
        print(f"{Colors.YELLOW}请检查端口是否被占用或应用是否已在运行。{Colors.END}")
        input("按任意键退出...")
        sys.exit(1)
