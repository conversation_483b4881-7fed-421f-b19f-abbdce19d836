{% extends "base.html" %}

{% block title %}照片分析器 - 设置{% endblock %}

{% block content %}
<div class="card">
    <h1 class="card-title">⚙️ 设置</h1>
    <p>管理照片文件夹路径和应用设置</p>
</div>

<!-- 主设置菜单 -->
<div id="main-settings" class="card">
    <div class="settings-main-menu">
        <div class="settings-grid">
            <div class="settings-card" onclick="showSettingsPage('file-management')">
                <div class="settings-icon">📁</div>
                <h3>文件管理</h3>
                <p>管理照片文件夹、上传照片和查看已添加的路径</p>
            </div>
            <div class="settings-card" onclick="showSettingsPage('system-info')">
                <div class="settings-icon">ℹ️</div>
                <h3>系统信息</h3>
                <p>查看系统状态、版本信息和支持格式</p>
            </div>
            <div class="settings-card" onclick="showSettingsPage('system-operations')">
                <div class="settings-icon">🔧</div>
                <h3>系统操作</h3>
                <p>导入导出设置、系统维护和其他操作</p>
            </div>
        </div>
    </div>
</div>

<!-- 文件管理页面 -->
<div id="file-management" class="settings-page" style="display: none;">
    <div class="card">
        <div class="settings-header">
            <button class="back-btn" onclick="showMainSettings()">← 返回</button>
            <h2 class="card-title">📁 文件管理</h2>
        </div>
    </div>

    <!-- 上传照片 -->
    <div class="card">
        <h3 class="card-title">📸 上传照片</h3>
        <div class="form-group">
            <label class="form-label">选择照片：</label>
            <div class="upload-options" style="margin-bottom: 1rem;">
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <button type="button" onclick="selectFiles()" class="btn btn-secondary">📄 选择文件</button>
                    <button type="button" onclick="selectPhotoFolder()" class="btn btn-secondary">📁 选择文件夹</button>
                </div>
                <small class="upload-hint" style="display: block; margin-top: 0.5rem; color: #666;">
                    选择文件：可选择多个照片文件 | 选择文件夹：自动上传文件夹内所有照片
                </small>
            </div>
            
            <div class="upload-area" id="upload-area">
                <div class="upload-content">
                    <div class="upload-icon">📁</div>
                    <p class="upload-text">点击上方按钮选择文件或文件夹，或拖拽照片到此处</p>
                    <small class="upload-hint">支持 JPG, JPEG, PNG, TIFF 格式</small>
                </div>
            </div>
            
            <!-- 隐藏的文件输入 -->
            <input type="file" id="photo-upload-files" multiple accept=".jpg,.jpeg,.png,.tiff,.tif" style="display: none;">
            <input type="file" id="photo-upload-folder" webkitdirectory directory multiple style="display: none;">
        </div>
        
        <!-- 上传进度显示 -->
        <div id="upload-progress" style="display: none; margin: 1rem 0;">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <p class="progress-text" id="progress-text">正在上传...</p>
        </div>
        
        <!-- 已选择的文件列表 -->
        <div id="selected-files" style="margin: 1rem 0;"></div>
        
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <button onclick="uploadPhotos()" class="btn btn-primary" id="upload-btn" disabled>📤 上传并分析</button>
            <button onclick="clearSelectedFiles()" class="btn btn-outline" style="border: 1px solid #ddd;">🗑️ 清空选择</button>
        </div>
    </div>

    <!-- 添加新路径 -->
    <div class="card">
        <h3 class="card-title">📁 添加照片文件夹</h3>
        <div class="form-group">
            <label for="new-path" class="form-label">文件夹路径：</label>
            <input type="text" id="new-path" class="form-input" placeholder="请输入照片文件夹的完整路径，例如：C:\Photos" style="width: 100%;">
            <small class="form-help" style="color: #666; font-size: 0.9em; margin-top: 0.5rem; display: block;">
                💡 提示：可以直接输入路径，或点击"浏览文件夹"按钮选择
            </small>
        </div>
        
        <!-- 当前选择的路径显示区域 -->
        <div id="current-selection" style="margin: 1rem 0;"></div>
        
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <button onclick="addPath()" class="btn btn-primary">➕ 添加路径</button>
            <button onclick="selectFolder()" class="btn btn-secondary">📂 浏览文件夹</button>
            <button onclick="showPathHelper()" class="btn btn-info">💡 路径助手</button>
            <button onclick="clearPath()" class="btn btn-outline" style="border: 1px solid #ddd;">🗑️ 清空</button>
        </div>
    </div>

    <!-- 已添加的路径列表 -->
    <div class="card">
        <h3 class="card-title">📋 已添加的照片路径</h3>
        
        {% if paths %}
        <div class="path-list">
            {% for path in paths %}
            <div class="path-item">
                <span class="path-text">{{ path }}</span>
                <button data-path="{{ path }}" onclick="removePath(this.dataset.path)" class="btn btn-danger btn-sm">🗑️ 删除</button>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">📂</div>
            <h4 class="empty-state-title">暂无照片路径</h4>
            <p class="empty-state-description">请添加包含照片的文件夹路径开始分析</p>
        </div>
        {% endif %}
    </div>
    
    <!-- 照片管理操作区域 -->
    <div class="card">
        <h3 class="card-title">🔧 照片管理操作</h3>
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <button onclick="analyzePhotos()" class="btn btn-success">🔍 重新分析所有照片</button>
            <button onclick="clearAllPaths()" class="btn btn-warning">🗑️ 清空所有路径</button>
        </div>
        
        <div class="alert alert-info" style="margin-top: 1rem;">
            <strong>重新分析：</strong> 点击此按钮将重新扫描所有已添加路径中的照片并更新统计数据。
        </div>
    </div>
</div>

<!-- 系统信息页面 -->
<div id="system-info" class="settings-page" style="display: none;">
    <div class="card">
        <div class="settings-header">
            <button class="back-btn" onclick="showMainSettings()">← 返回</button>
            <h2 class="card-title">ℹ️ 系统信息</h2>
        </div>
    </div>
    
    <div class="card">
        <h3 class="card-title">📊 系统状态</h3>
        <div class="system-info-grid">
            <div class="info-item">
                <div class="info-icon">🚀</div>
                <div class="info-content">
                    <h4>应用版本</h4>
                    <p>1.0.0</p>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon">🖼️</div>
                <div class="info-content">
                    <h4>支持格式</h4>
                    <p>JPEG, PNG, TIFF</p>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon">⚙️</div>
                <div class="info-content">
                    <h4>配置文件</h4>
                    <p>config.json</p>
                </div>
            </div>
            <div class="info-item">
                <div class="info-icon">💾</div>
                <div class="info-content">
                    <h4>数据目录</h4>
                    <p>./data</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h3 class="card-title">📈 运行统计</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ paths|length if paths else 0 }}</div>
                <div class="stat-label">已添加路径</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">-</div>
                <div class="stat-label">已分析照片</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">-</div>
                <div class="stat-label">存储空间</div>
            </div>
        </div>
    </div>
</div>

<!-- 系统操作页面 -->
<div id="system-operations" class="settings-page" style="display: none;">
    <div class="card">
        <div class="settings-header">
            <button class="back-btn" onclick="showMainSettings()">← 返回</button>
            <h2 class="card-title">🔧 系统操作</h2>
        </div>
    </div>
    
    <div class="card">
        <h3 class="card-title">📁 数据管理</h3>
        <div class="operation-grid">
            <div class="operation-card">
                <div class="operation-icon">📤</div>
                <h4>导出设置</h4>
                <p>导出当前配置和路径设置</p>
                <button onclick="exportSettings()" class="btn btn-primary">导出</button>
            </div>
            <div class="operation-card">
                <div class="operation-icon">📥</div>
                <h4>导入设置</h4>
                <p>从文件导入配置和路径设置</p>
                <button onclick="importSettings()" class="btn btn-secondary">导入</button>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h3 class="card-title">🔄 系统维护</h3>
        <div class="maintenance-actions">
            <button onclick="refreshSystem()" class="btn btn-info">🔄 刷新系统</button>
            <button onclick="clearCache()" class="btn btn-warning">🗑️ 清除缓存</button>
            <button onclick="resetSettings()" class="btn btn-danger">⚠️ 重置设置</button>
        </div>
        <div class="alert alert-warning" style="margin-top: 1rem;">
            <strong>⚠️ 注意：</strong> 重置设置将清空所有配置，请谨慎操作！
        </div>
    </div>
    
    <div class="card">
        <h3 class="card-title">🏠 快捷操作</h3>
        <div class="quick-actions">
            <a href="/" class="btn btn-primary">🏠 返回主页</a>
            <a href="/data" class="btn btn-secondary">📊 查看数据</a>
            <a href="/devices" class="btn btn-info">📱 设备信息</a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面导航功能
function showMainSettings() {
    document.getElementById('main-settings').style.display = 'block';
    document.querySelectorAll('.settings-page').forEach(page => {
        page.style.display = 'none';
    });
}

function showSettingsPage(pageId) {
    document.getElementById('main-settings').style.display = 'none';
    document.querySelectorAll('.settings-page').forEach(page => {
        page.style.display = 'none';
    });
    document.getElementById(pageId).style.display = 'block';
}

// 文件选择功能
function selectFiles() {
    document.getElementById('photo-upload-files').click();
}

function selectPhotoFolder() {
    document.getElementById('photo-upload-folder').click();
}

function selectFolder() {
    // 这里可以添加文件夹选择逻辑
    alert('请在输入框中输入文件夹路径，或使用文件管理器复制路径');
}

function showPathHelper() {
    const helpText = `
路径格式示例：
Windows: C:\\Users\\<USER>\\Pictures
macOS/Linux: /home/<USER>/Pictures

提示：
1. 可以直接从文件管理器复制路径
2. 确保路径存在且包含照片文件
3. 支持的照片格式：JPG, JPEG, PNG, TIFF
    `;
    alert(helpText);
}

function clearPath() {
    document.getElementById('new-path').value = '';
    document.getElementById('current-selection').innerHTML = '';
}

function clearSelectedFiles() {
    document.getElementById('selected-files').innerHTML = '';
    document.getElementById('upload-btn').disabled = true;
}

// 路径管理功能
function addPath() {
    const path = document.getElementById('new-path').value.trim();
    if (!path) {
        showToast('请输入有效的文件夹路径', 'error');
        return;
    }
    
    fetch('/api/add-path', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({path: path})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('路径添加成功', 'success');
            location.reload();
        } else {
            showToast('添加失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('添加失败: ' + error.message, 'error');
    });
}

function removePath(path) {
    if (confirm('确定要删除此路径吗？')) {
        fetch('/api/remove-path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({path: path})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('路径删除成功', 'success');
                location.reload();
            } else {
                showToast('删除失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('删除失败: ' + error.message, 'error');
        });
    }
}

function clearAllPaths() {
    if (confirm('确定要清空所有照片路径吗？此操作不可恢复。')) {
        fetch('/api/clear-all-paths', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('所有路径已清空', 'success');
                location.reload();
            } else {
                showToast('清空失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('清空失败: ' + error.message, 'error');
        });
    }
}

function analyzePhotos() {
    if (confirm('确定要重新分析所有照片吗？这可能需要一些时间。')) {
        showToast('开始重新分析照片...', 'info');
        fetch('/api/analyze-all-photos', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('照片分析完成', 'success');
                location.reload();
            } else {
                showToast('分析失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('分析失败: ' + error.message, 'error');
        });
    }
}

// 上传功能
function uploadPhotos() {
    // 这里添加上传逻辑
    showToast('上传功能开发中...', 'info');
}

// 导出/导入设置
function exportSettings() {
    fetch('/api/export-settings')
        .then(response => response.blob())
        .then(blob => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `photo-analyzer-settings-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            showToast('设置导出成功', 'success');
        })
        .catch(error => {
            showToast('导出失败: ' + error.message, 'error');
        });
}

function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (event) => {
        const file = event.target.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('file', file);
            
            fetch('/api/import-settings', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('设置导入成功', 'success');
                    location.reload();
                } else {
                    showToast('导入失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showToast('导入失败: ' + error.message, 'error');
            });
        }
    };
    input.click();
}

// 系统维护功能
function refreshSystem() {
    showToast('系统刷新中...', 'info');
    location.reload();
}

function clearCache() {
    if (confirm('确定要清除缓存吗？')) {
        fetch('/api/clear-cache', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('缓存清除成功', 'success');
            } else {
                showToast('清除失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('清除失败: ' + error.message, 'error');
        });
    }
}

function resetSettings() {
    if (confirm('确定要重置所有设置吗？这将清空所有配置！')) {
        if (confirm('最后确认：此操作不可恢复，确定要继续吗？')) {
            fetch('/api/reset-settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('设置重置成功', 'success');
                    location.reload();
                } else {
                    showToast('重置失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showToast('重置失败: ' + error.message, 'error');
            });
        }
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196f3'};
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(toast);
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
`;
document.head.appendChild(style);
</script>
{% endblock %}
