{% extends "base.html" %}

{% block title %}主页 | 照片分析器{% endblock %}

{% block content %}
<div class="card">
    <h1 class="card-title">📸 照片分析概览</h1>
    <!-- 基础统计信息 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">📷 {{ total_photos if has_data else 0 }}</div>
            <div class="stat-label">照片总数</div>
        </div>
    </div>
    
    {% if not has_data %}
    <!-- 空状态展示 -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="4" width="18" height="15" rx="2" stroke="currentColor" stroke-width="2"/>
                <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="7.5" cy="7.5" r="1.5" fill="currentColor"/>
            </svg>
        </div>
        <h2 class="empty-state-title">开始分析您的照片</h2>
        <p class="empty-state-description">
            {% if config.paths|length > 0 %}
                已配置 {{ config.paths|length }} 个监控路径，点击下方按钮开始分析照片数据。
            {% else %}
                请先在设置页面添加包含照片的文件夹路径，我们将自动分析EXIF信息并生成详细的统计报告。
            {% endif %}
        </p>
        <div class="empty-state-actions">
            {% if config.paths|length > 0 %}
                <button onclick="analyzePhotos()" class="btn btn-primary">
                    <span>🔍</span> 开始分析照片
                </button>
            {% endif %}
            <a href="/settings" class="btn btn-secondary">
                <span>⚙️</span> 管理路径
            </a>
        </div>
    </div>
    {% else %}
    <!-- 数据展示区域 -->
    <div class="main-content-area">
        <!-- 设备镜头统计信息卡片 -->
        <div class="info-cards-container">
            <div class="info-card">
                <div class="info-card-header">
                    <h3>� 设备统计</h3>
                </div>
                <div class="info-card-content">
                    <div class="stat-row">
                        <span class="stat-label">最常用设备:</span>
                        <span class="stat-value" id="top-device">加载中...</span>
                        <span class="stat-count" id="top-device-count"></span>
                    </div>
                </div>
            </div>

            <div class="info-card">
                <div class="info-card-header">
                    <h3>🔍 镜头统计</h3>
                </div>
                <div class="info-card-content">
                    <div class="stat-row">
                        <span class="stat-label">最常用镜头:</span>
                        <span class="stat-value" id="top-lens">加载中...</span>
                        <span class="stat-count" id="top-lens-count"></span>
                    </div>
                </div>
            </div>

            <div class="info-card">
                <div class="info-card-header">
                    <h3>⭐ 常用搭配</h3>
                </div>
                <div class="info-card-content">
                    <div class="stat-row">
                        <span class="stat-label">设备+镜头:</span>
                        <span class="stat-value" id="device-lens-combo">加载中...</span>
                        <span class="stat-count" id="device-lens-combo-count"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表展示区域 -->
        <div class="charts-section">
            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>📅 月份拍摄趋势</h3>
                        <p class="chart-subtitle">按月份统计拍摄活动</p>
                    </div>
                    <div id="months-chart" class="chart-content"></div>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3>🕐 时段拍摄分布</h3>
                        <p class="chart-subtitle">一天中的拍摄时间偏好</p>
                    </div>
                    <div id="time-periods-chart" class="chart-content"></div>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="actions-container">
            <button onclick="analyzePhotos()" class="btn btn-primary">
                <span>🔄</span> 更新数据分析
            </button>
            <a href="/data" class="btn btn-secondary">
                <span>📊</span> 查看详细数据
            </a>
            <a href="/settings" class="btn btn-outline">
                <span>⚙️</span> 管理设置
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{% if has_data %}
<script>
// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    // 延迟加载图表
    setTimeout(loadOverviewCharts, 200);
});

// 确保页面加载完成后执行
console.log('开始加载图表...');
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(loadOverviewCharts, 200);
    });
} else {
    setTimeout(loadOverviewCharts, 200);
}
</script>
{% endif %}
{% endblock %}