/* 现代化界面的额外样式 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* 美化空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 1rem;
    margin: 2rem auto;
    max-width: 600px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.empty-state-icon {
    font-size: 5rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
}

.empty-state-title {
    font-size: 1.8rem;
    margin-bottom: 1.2rem;
    color: #2d3748;
    font-weight: 700;
}

.empty-state-description {
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 1.1rem;
}

/* 添加平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 美化图表容器 */
.chart-container {
    position: relative;
    transition: all 0.2s ease;
}

.chart-container:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 导航链接增强 */
.nav-links a span {
    margin-right: 0.3rem;
    opacity: 0.8;
}

/* 按钮增强 */
.btn {
    font-weight: 600;
    letter-spacing: 0.5px;
    border-radius: 12px;
    padding: 12px 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(102, 126, 234, 0.3);
    color: #4a5568;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-2px);
}

/* 按钮脉冲效果 */
.btn-primary:focus {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* 新设置界面样式 */

/* 主设置菜单 */
.settings-main-menu {
    padding: 1rem 0;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.settings-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.settings-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.settings-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.settings-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: white;
}

.settings-card p {
    opacity: 0.9;
    line-height: 1.5;
    margin: 0;
}

/* 设置页面 */
.settings-page {
    animation: fadeIn 0.3s ease-in-out;
}

.settings-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.back-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: #5a6fd8;
    transform: translateX(-2px);
}

/* 系统信息网格 */
.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.info-icon {
    font-size: 2rem;
    color: #667eea;
}

.info-content h4 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
    font-size: 1rem;
}

.info-content p {
    margin: 0;
    color: #4a5568;
    font-size: 0.9rem;
}

/* 运行统计 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 操作网格 */
.operation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.operation-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.operation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.operation-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.operation-card h4 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
    font-size: 1.1rem;
}

.operation-card p {
    margin: 0 0 1rem 0;
    color: #4a5568;
    font-size: 0.9rem;
}

.operation-card .btn {
    width: 100%;
}

/* 系统维护操作 */
.maintenance-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.maintenance-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* 快捷操作 */
.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.quick-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* 路径列表样式 */
.path-list {
    margin-top: 1rem;
}

.path-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.path-text {
    flex: 1;
    margin-right: 1rem;
    font-family: 'Courier New', monospace;
    color: #2d3748;
}

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #667eea;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    margin: 1rem 0;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area:hover {
    border-color: #5a6fd8;
    background: #f0f4ff;
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.upload-text {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #4a5568;
    font-size: 0.9rem;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    color: #4a5568;
    font-size: 0.9rem;
    margin: 0;
}

/* 警告框样式 */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.alert-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    color: #0d47a1;
}

.alert-warning {
    background: #fff3e0;
    border: 1px solid #ffcc02;
    color: #e65100;
}

/* EXIF分析页面样式 */
.analysis-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.analysis-selector {
    margin-bottom: 30px;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.analysis-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.analysis-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.analysis-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.analysis-card:hover::before {
    transform: scaleX(1);
}

.card-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    text-align: center;
}

.analysis-card h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-weight: 600;
}

.analysis-card p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.batch-analysis-section {
    text-align: center;
    margin: 40px 0;
    padding: 30px;
    background: var(--card-bg);
    border-radius: 12px;
    border: 2px dashed var(--border-color);
}

.batch-analysis-section .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.batch-analysis-section .btn span {
    margin-right: 10px;
}

.custom-instruction-section {
    margin-bottom: 30px;
}

.custom-instruction-section h3 {
    margin-bottom: 15px;
    color: var(--text-color);
}

.custom-instruction-section textarea {
    width: 100%;
    min-height: 100px;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    background: var(--card-bg);
    color: var(--text-color);
}

.custom-instruction-section textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(54, 175, 255, 0.1);
}

.analysis-results {
    margin-top: 40px;
    padding: 30px;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.analysis-results h3 {
    margin: 0 0 25px 0;
    color: var(--text-color);
    font-size: 1.5rem;
    font-weight: 600;
}

.result-section {
    margin-bottom: 30px;
    padding: 25px;
    background: var(--bg-color);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.result-section.error {
    border-left-color: #ff6b6b;
}

.result-section h4 {
    margin: 0 0 20px 0;
    color: var(--text-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.result-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

.formatted-result {
    display: grid;
    gap: 15px;
}

.result-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 12px;
    background: var(--card-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.result-item strong {
    color: var(--text-color);
    min-width: 150px;
    font-weight: 600;
}

.result-item span {
    flex: 1;
    color: var(--text-secondary);
}

.result-item ul {
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
}

.result-item ul li {
    margin: 5px 0;
}

.error-message {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 20px;
    color: #c53030;
    margin: 20px 0;
}

.error-message h4 {
    margin: 0 0 10px 0;
    color: #c53030;
}

.batch-results {
    display: grid;
    gap: 25px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-overlay p {
    color: white;
    font-size: 1.1rem;
    margin: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-charts-container {
        gap: 1.5rem;
    }
    
    .stats-info-container {
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .device-lens-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1.5rem 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .chart-container {
        padding: 1.2rem;
        border-radius: 12px;
    }
    
    .chart-title {
        font-size: 1.1rem;
    }
    
    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
        padding: 15px 18px;
    }
    
    .stat-value {
        margin-left: 0;
        margin-top: 8px;
        font-size: 0.95rem;
    }
    
    .stat-count {
        margin-left: 0;
        margin-top: 6px;
        align-self: flex-start;
        font-size: 0.8rem;
    }
    
    .logo {
        font-size: 1.2rem;
    }
    
    .nav-links {
        gap: 0.6rem;
    }
    
    .nav-links a {
        padding: 0.3rem 0.5rem;
        font-size: 0.9rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.85rem;
        margin: 0.25rem;
    }
    
    .card {
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .card-title {
        font-size: 1.3rem;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .system-info-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .operation-grid {
        grid-template-columns: 1fr;
    }
    
    .maintenance-actions {
        flex-direction: column;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .path-item {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
    
    .path-text {
        margin-right: 0;
        word-break: break-all;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }
    
    .stat-card {
        padding: 1.2rem 0.8rem;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .empty-state {
        padding: 3rem 1rem;
    }
    
    .empty-state-icon {
        font-size: 4rem;
    }
    
    .empty-state-title {
        font-size: 1.5rem;
    }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .error-message {
        background: #2d1b1b;
        border-color: #4a2626;
        color: #f56565;
    }
    
    .error-message h4 {
        color: #f56565;
    }
}
