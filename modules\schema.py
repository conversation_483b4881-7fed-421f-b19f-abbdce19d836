"""
图片分析结构化输出Schema定义
用于定义LLM输出的数据结构
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from enum import Enum

class PhotoCategory(str, Enum):
    """图片分类"""
    PERSON = "人物"
    LANDSCAPE = "风景"
    OBJECT = "物体"
    ANIMAL = "动物"
    BUILDING = "建筑"
    FOOD = "食物"
    TECHNOLOGY = "科技"
    ART = "艺术"
    SPORTS = "运动"
    OTHER = "其他"

class EmotionType(str, Enum):
    """情绪类型"""
    HAPPY = "开心"
    SAD = "悲伤"
    NEUTRAL = "中性"
    EXCITED = "兴奋"
    PEACEFUL = "平静"
    ENERGETIC = "活跃"
    ROMANTIC = "浪漫"
    MYSTERIOUS = "神秘"

class QualityLevel(str, Enum):
    """质量等级"""
    EXCELLENT = "优秀"
    GOOD = "良好"
    AVERAGE = "一般"
    POOR = "较差"

class ShootingStyle(str, Enum):
    """拍摄风格"""
    PORTRAIT = "人像"
    LANDSCAPE = "风景"
    STREET = "街拍"
    MACRO = "微距"
    SPORTS = "运动"
    NIGHT = "夜景"
    DOCUMENTARY = "纪实"
    ABSTRACT = "抽象"
    WILDLIFE = "野生动物"
    ARCHITECTURE = "建筑"

class StabilityLevel(str, Enum):
    """手持稳定度"""
    EXCELLENT = "非常稳定"
    GOOD = "稳定"
    AVERAGE = "一般"
    POOR = "不稳定"
    VERY_POOR = "很不稳定"

class DetectedObject(BaseModel):
    """检测到的物体"""
    name: str = Field(..., description="物体名称")
    confidence: float = Field(..., description="置信度", ge=0.0, le=1.0)
    position: Optional[str] = Field(None, description="物体位置描述")

class ColorAnalysis(BaseModel):
    """颜色分析"""
    dominant_colors: List[str] = Field(..., description="主要颜色", max_items=5)
    color_harmony: str = Field(..., description="色彩和谐度描述")
    mood_from_color: str = Field(..., description="颜色传达的情绪")

class CompositionAnalysis(BaseModel):
    """构图分析"""
    composition_type: str = Field(..., description="构图类型")
    rule_of_thirds: bool = Field(..., description="是否符合三分法")
    balance: str = Field(..., description="平衡性评价")
    focal_point: str = Field(..., description="焦点描述")

class TechnicalAnalysis(BaseModel):
    """技术分析"""
    sharpness: QualityLevel = Field(..., description="清晰度")
    exposure: QualityLevel = Field(..., description="曝光度")
    lighting: str = Field(..., description="光线条件")
    estimated_camera_settings: Optional[str] = Field(None, description="推测的相机设置")

class PhotoAnalysisResult(BaseModel):
    """完整的图片分析结果"""
    # 基本信息
    title: str = Field(..., description="图片标题")
    description: str = Field(..., description="详细描述", max_length=500)
    category: PhotoCategory = Field(..., description="图片分类")
    
    # 检测结果
    detected_objects: List[DetectedObject] = Field(..., description="检测到的物体")
    people_count: int = Field(..., description="人物数量", ge=0)
    
    # 情绪和氛围
    emotion: EmotionType = Field(..., description="图片情绪")
    atmosphere: str = Field(..., description="氛围描述")
    
    # 技术分析
    technical_analysis: TechnicalAnalysis = Field(..., description="技术分析")
    
    # 艺术分析
    color_analysis: ColorAnalysis = Field(..., description="颜色分析")
    composition_analysis: CompositionAnalysis = Field(..., description="构图分析")
    
    # 评分
    overall_quality: QualityLevel = Field(..., description="整体质量")
    aesthetic_score: float = Field(..., description="美学评分", ge=0.0, le=10.0)
    
    # 建议
    improvement_suggestions: List[str] = Field(..., description="改进建议", max_items=3)
    tags: List[str] = Field(..., description="标签", max_items=10)

class BatchAnalysisResult(BaseModel):
    """批量分析结果"""
    total_images: int = Field(..., description="总图片数")
    processed_images: int = Field(..., description="已处理图片数")
    failed_images: int = Field(..., description="失败图片数")
    
    # 统计信息
    category_distribution: Dict[PhotoCategory, int] = Field(..., description="分类分布")
    emotion_distribution: Dict[EmotionType, int] = Field(..., description="情绪分布")
    quality_distribution: Dict[QualityLevel, int] = Field(..., description="质量分布")
    
    # 总体分析
    average_aesthetic_score: float = Field(..., description="平均美学评分", ge=0.0, le=10.0)
    most_common_objects: List[str] = Field(..., description="最常见物体", max_items=10)
    dominant_colors_overall: List[str] = Field(..., description="整体主色调", max_items=5)
    
    # 建议
    collection_summary: str = Field(..., description="图片集合总结")
    collection_recommendations: List[str] = Field(..., description="集合建议", max_items=5)

class QuickAnalysisResult(BaseModel):
    """快速分析结果"""
    title: str = Field(..., description="简短标题")
    description: str = Field(..., description="简短描述", max_length=200)
    category: PhotoCategory = Field(..., description="图片分类")
    main_objects: List[str] = Field(..., description="主要物体", max_items=3)
    emotion: EmotionType = Field(..., description="图片情绪")
    quality_score: float = Field(..., description="质量评分", ge=0.0, le=10.0)
    quality: QualityLevel = Field(..., description="质量评级")
    tags: List[str] = Field(..., description="标签", max_items=5)

class FocalLengthAnalysis(BaseModel):
    """焦段分析"""
    most_used_focal_length: str = Field(..., description="最常用焦段")
    focal_length_distribution: Dict[str, int] = Field(..., description="焦段分布统计")
    preferred_range: str = Field(..., description="偏好焦段范围")
    shooting_style_inference: List[ShootingStyle] = Field(..., description="推测拍摄风格")
    wide_angle_usage: float = Field(..., description="广角使用比例", ge=0.0, le=1.0)
    telephoto_usage: float = Field(..., description="长焦使用比例", ge=0.0, le=1.0)
    standard_usage: float = Field(..., description="标准焦段使用比例", ge=0.0, le=1.0)
    recommendations: List[str] = Field(..., description="焦段使用建议", max_items=3)

class ApertureAnalysis(BaseModel):
    """光圈分析"""
    most_used_aperture: str = Field(..., description="最常用光圈")
    aperture_distribution: Dict[str, int] = Field(..., description="光圈分布统计")
    average_aperture: float = Field(..., description="平均光圈值")
    depth_of_field_preference: str = Field(..., description="景深偏好")
    low_light_performance: str = Field(..., description="低光性能评估")
    portrait_aperture_usage: float = Field(..., description="人像光圈使用比例", ge=0.0, le=1.0)
    landscape_aperture_usage: float = Field(..., description="风景光圈使用比例", ge=0.0, le=1.0)
    recommendations: List[str] = Field(..., description="光圈使用建议", max_items=3)

class ShutterSpeedAnalysis(BaseModel):
    """快门速度分析"""
    most_used_shutter_speed: str = Field(..., description="最常用快门速度")
    shutter_speed_distribution: Dict[str, int] = Field(..., description="快门速度分布统计")
    average_shutter_speed: str = Field(..., description="平均快门速度")
    handheld_capability: str = Field(..., description="手持拍摄能力评估")
    motion_blur_risk: str = Field(..., description="运动模糊风险评估")
    fast_shutter_usage: float = Field(..., description="高速快门使用比例", ge=0.0, le=1.0)
    slow_shutter_usage: float = Field(..., description="慢速快门使用比例", ge=0.0, le=1.0)
    recommendations: List[str] = Field(..., description="快门速度使用建议", max_items=3)

class ISOAnalysis(BaseModel):
    """ISO分析"""
    most_used_iso: str = Field(..., description="最常用ISO")
    iso_distribution: Dict[str, int] = Field(..., description="ISO分布统计")
    average_iso: float = Field(..., description="平均ISO值")
    noise_tolerance: str = Field(..., description="噪点容忍度评估")
    low_light_frequency: float = Field(..., description="低光拍摄频率", ge=0.0, le=1.0)
    daylight_shooting_ratio: float = Field(..., description="日光拍摄比例", ge=0.0, le=1.0)
    high_iso_confidence: str = Field(..., description="高ISO使用信心")
    recommendations: List[str] = Field(..., description="ISO使用建议", max_items=3)

class CameraBodyAnalysis(BaseModel):
    """机身分析"""
    primary_camera: str = Field(..., description="主要使用机身")
    camera_distribution: Dict[str, int] = Field(..., description="机身使用分布")
    camera_type: str = Field(..., description="相机类型")
    shooting_volume: int = Field(..., description="拍摄量")
    consistency_score: float = Field(..., description="使用一致性评分", ge=0.0, le=10.0)
    upgrade_suggestions: List[str] = Field(..., description="升级建议", max_items=3)

class LensAnalysis(BaseModel):
    """镜头分析"""
    most_used_lens: str = Field(..., description="最常用镜头")
    lens_distribution: Dict[str, int] = Field(..., description="镜头使用分布")
    lens_type_preference: str = Field(..., description="镜头类型偏好")
    focal_length_coverage: str = Field(..., description="焦段覆盖范围")
    prime_vs_zoom_ratio: Dict[str, float] = Field(..., description="定焦vs变焦比例")
    specialization_level: str = Field(..., description="专业化程度")
    recommendations: List[str] = Field(..., description="镜头使用建议", max_items=3)

class UserPreferenceAnalysis(BaseModel):
    """用户喜好分析"""
    shooting_style: List[ShootingStyle] = Field(..., description="拍摄风格偏好")
    subject_preferences: List[str] = Field(..., description="拍摄主题偏好", max_items=5)
    technical_level: str = Field(..., description="技术水平评估")
    risk_taking_tendency: str = Field(..., description="冒险倾向")
    gear_usage_pattern: str = Field(..., description="器材使用模式")
    shooting_frequency: str = Field(..., description="拍摄频率")
    time_preference: str = Field(..., description="拍摄时间偏好")
    creativity_score: float = Field(..., description="创造力评分", ge=0.0, le=10.0)
    improvement_areas: List[str] = Field(..., description="改进方向", max_items=3)

class HandStabilityAnalysis(BaseModel):
    """手持稳定性分析"""
    overall_stability: StabilityLevel = Field(..., description="整体稳定性")
    stability_by_focal_length: Dict[str, StabilityLevel] = Field(..., description="不同焦段稳定性")
    safe_shutter_speed_adherence: float = Field(..., description="安全快门遵守率", ge=0.0, le=1.0)
    risk_shots_percentage: float = Field(..., description="风险拍摄比例", ge=0.0, le=1.0)
    stabilization_reliance: str = Field(..., description="防抖依赖程度")
    improvement_suggestions: List[str] = Field(..., description="稳定性改进建议", max_items=3)
    tripod_recommendation: str = Field(..., description="三脚架使用建议")

class ComprehensiveAnalysis(BaseModel):
    """综合分析"""
    photographer_profile: str = Field(..., description="摄影师画像")
    skill_level_assessment: str = Field(..., description="技能水平评估")
    gear_optimization_score: float = Field(..., description="器材优化评分", ge=0.0, le=10.0)
    shooting_efficiency: float = Field(..., description="拍摄效率评分", ge=0.0, le=10.0)
    technical_consistency: float = Field(..., description="技术一致性评分", ge=0.0, le=10.0)
    creative_exploration: float = Field(..., description="创意探索评分", ge=0.0, le=10.0)
    strengths: List[str] = Field(..., description="优势", max_items=5)
    weaknesses: List[str] = Field(..., description="不足", max_items=5)
    next_steps: List[str] = Field(..., description="下一步建议", max_items=5)

# 已有的其他schema类...
