#!/usr/bin/env python3
"""
EXIF分析使用示例
演示如何使用新的EXIF分析功能
"""

import os
import sys
import json
import traceback
from pathlib import Path

# 添加模块路径
sys.path.append(os.path.dirname(__file__))

from modules.llm_api import analyze_exif_data, EXIFAnalyzer, LLMError

def main():
    """主函数"""
    print("🔍 EXIF数据分析示例")
    print("=" * 50)
    
    # 检查CSV文件
    csv_path = "data/raw.csv"
    if not os.path.exists(csv_path):
        print(f"❌ 错误: 找不到CSV文件 {csv_path}")
        print("请先运行应用程序并添加照片路径进行分析")
        return
    
    # 示例1: 使用便捷函数进行单个分析
    print("\n📍 示例1: 使用便捷函数分析焦段使用习惯")
    try:
        result = analyze_exif_data(csv_path, "focal_length")
        print(f"✅ 分析成功!")
        print(f"最常用焦段: {result.most_used_focal_length}")
        print(f"偏好范围: {result.preferred_range}")
        print(f"建议: {result.recommendations}")
    except LLMError as e:
        print(f"❌ LLM错误: {e}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    # 示例2: 使用分析器类进行多个分析
    print("\n📍 示例2: 使用EXIFAnalyzer进行多个分析")
    try:
        analyzer = EXIFAnalyzer()
        
        # 分析光圈使用习惯
        print("\n🔍 分析光圈使用习惯...")
        aperture_result = analyzer.analyze_aperture(csv_path)
        print(f"最常用光圈: {aperture_result.most_used_aperture}")
        print(f"景深偏好: {aperture_result.depth_of_field_preference}")
        
        # 分析快门速度
        print("\n⚡ 分析快门速度...")
        shutter_result = analyzer.analyze_shutter_speed(csv_path)
        print(f"最常用快门速度: {shutter_result.most_used_shutter_speed}")
        print(f"手持能力评估: {shutter_result.handheld_capability}")
        
        # 分析手持稳定性
        print("\n🤚 分析手持稳定性...")
        stability_result = analyzer.analyze_hand_stability(csv_path)
        print(f"整体稳定性: {stability_result.overall_stability}")
        print(f"安全快门遵守率: {stability_result.safe_shutter_speed_adherence:.2%}")
        
    except LLMError as e:
        print(f"❌ LLM错误: {e}")
    except Exception as e:
        print(f"❌ 错误: {e}")
        traceback.print_exc()
    
    # 示例3: 综合分析和用户喜好分析
    print("\n📍 示例3: 综合分析和用户喜好分析")
    try:
        analyzer = EXIFAnalyzer()
        
        # 用户喜好分析
        print("\n👤 分析用户喜好...")
        preference_result = analyzer.analyze_user_preference(csv_path)
        print(f"拍摄风格: {preference_result.shooting_style}")
        print(f"技术水平: {preference_result.technical_level}")
        print(f"创造力评分: {preference_result.creativity_score}/10")
        
        # 综合分析
        print("\n📊 综合分析...")
        comprehensive_result = analyzer.analyze_comprehensive(csv_path)
        print(f"摄影师画像: {comprehensive_result.photographer_profile}")
        print(f"技能水平: {comprehensive_result.skill_level_assessment}")
        print(f"优势: {comprehensive_result.strengths}")
        print(f"改进建议: {comprehensive_result.next_steps}")
        
    except LLMError as e:
        print(f"❌ LLM错误: {e}")
    except Exception as e:
        print(f"❌ 错误: {e}")
        traceback.print_exc()
    
    # 示例4: 自定义指令分析
    print("\n📍 示例4: 使用自定义指令")
    try:
        custom_instruction = "请特别关注夜景拍摄和高ISO使用情况，提供夜景摄影的专业建议"
        
        iso_result = analyze_exif_data(csv_path, "iso", custom_instruction)
        print(f"✅ 使用自定义指令的ISO分析完成!")
        print(f"平均ISO: {iso_result.average_iso}")
        print(f"低光拍摄频率: {iso_result.low_light_frequency:.2%}")
        print(f"建议: {iso_result.recommendations}")
        
    except LLMError as e:
        print(f"❌ LLM错误: {e}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print("\n🎉 示例演示完成!")
    print("您可以在Web界面中体验更多功能: http://localhost:8000/exif-analysis")

if __name__ == "__main__":
    main()
