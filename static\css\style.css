/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    color: #212529;
    line-height: 1.6;
}

.container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 1rem;
}

/* 顶部导航 */
.navbar {
    background: #ffffff;
    padding: 0.8rem 0;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 1rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: 600;
    color: #3b7ff2;
}

.nav-links {
    display: flex;
    gap: 1.2rem;
    list-style: none;
}

.nav-links a {
    text-decoration: none;
    color: #495057;
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.nav-links a:hover,
.nav-links a.active {
    background: #3b7ff2;
    color: white;
}

/* 卡片样式 */
.card {
    background: #ffffff;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: #1e293b;
    font-weight: 700;
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
}

.card-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* 统计数据卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 2rem 1.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.35);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.95rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

/* 主页布局优化 */
.main-charts-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stats-info-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e8f2ff 100%);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.stats-info-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stats-info-container .chart-title {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #2d3748;
    font-size: 1.3rem;
    font-weight: 800;
}

/* 图表容器 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
    margin-bottom: 0;
}

.chart-container {
    background: #ffffff;
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.chart-container:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chart-title {
    font-size: 1.2rem;
    margin-bottom: 1.2rem;
    color: #2d3748;
    font-weight: 700;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;
}

.chart-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.2rem;
}

.form-label {
    display: block;
    margin-bottom: 0.4rem;
    font-weight: 500;
    color: #495057;
    font-size: 0.95rem;
}

.form-input {
    width: 100%;
    padding: 0.65rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.95rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: #f8f9fa;
}

.form-input:focus {
    outline: none;
    border-color: #3b7ff2;
    box-shadow: 0 0 0 3px rgba(59, 127, 242, 0.15);
    background-color: #ffffff;
}

/* 按钮样式 */
.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 4px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(100, 116, 139, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6375 0%, #3f4755 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.25);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
    border: none;
}

.btn-info:hover {
    background-color: #138496;
}

/* 路径列表 */
.path-list {
    margin-bottom: 2rem;
}

.path-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.9);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    border: 1px solid #e9ecef;
}

.path-text {
    font-family: 'Courier New', monospace;
    color: #495057;
    flex-grow: 1;
    margin-right: 1rem;
}

/* 消息提示 */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #cce7ff;
    border-color: #b8daff;
    color: #004085;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #856404;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 2rem;
}

.spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e8f2ff 100%);
    border-radius: 20px;
    margin: 2rem 0;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.empty-state-icon {
    margin-bottom: 2rem;
    color: #667eea;
    opacity: 0.7;
}

.empty-state-icon svg {
    width: 80px;
    height: 80px;
}

.empty-state-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.empty-state-description {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* 主页新布局样式 */
.main-content-area {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
    margin-top: 1.5rem;
}

/* 信息卡片容器 */
.info-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 16px 16px 0 0;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.2);
}

.info-card-header {
    margin-bottom: 1rem;
}

.info-card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #334155;
    margin: 0;
}

.info-card-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.stat-row {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.stat-row .stat-label {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
    text-transform: none;
    letter-spacing: 0;
}

.stat-row .stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    word-break: break-all;
}

.stat-row .stat-count {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 500;
}

/* 图表区域样式 */
.charts-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e8f2ff 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.08);
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
}

.chart-container {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
}

.chart-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.chart-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 0.5rem 0;
}

.chart-subtitle {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
    font-weight: 400;
}

.chart-content {
    height: 350px;
    border-radius: 8px;
    overflow: hidden;
}

/* 路径选择相关样式 */
.form-help {
    color: #666;
    font-size: 0.9em;
    margin-top: 0.5rem;
    display: block;
}

.btn-outline {
    background: transparent;
    border: 1px solid #ddd;
    color: #666;
    transition: all 0.2s ease;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #999;
    color: #333;
}

.path-selection-info {
    background: #e8f4f8;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.path-selection-info code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #d0d7de;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafbfc;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-area:hover {
    border-color: #3b7ff2;
    background-color: #f6f8fa;
}

.upload-area.dragover {
    border-color: #3b7ff2;
    background-color: #e6f3ff;
    transform: scale(1.02);
}

.upload-area {
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(59, 127, 242, 0.05), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-area.dragover::before {
    opacity: 1;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.upload-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: #495057;
    margin: 0;
}

.upload-hint {
    color: #6c757d;
    margin: 0;
}

/* 上传选项按钮样式 */
.upload-options {
    margin-bottom: 1rem;
}

.upload-options .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.upload-options .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b7ff2, #28a745);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    color: #495057;
    font-weight: 500;
    margin: 0;
}

/* 文件列表样式 */
.file-list {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-icon {
    font-size: 1.2rem;
}

.file-name {
    font-weight: 500;
    color: #495057;
}

.file-size {
    font-size: 0.9rem;
    color: #6c757d;
}

.file-status {
    font-size: 0.9rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
}

.file-status.ready {
    background-color: #d1ecf1;
    color: #0c5460;
}

.file-status.uploading {
    background-color: #fff3cd;
    color: #856404;
}

.file-status.success {
    background-color: #d4edda;
    color: #155724;
}

.file-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 文件夹信息显示 */
.folder-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #1565c0;
}

/* 设备镜头统计样式 */
.device-lens-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 0;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 24px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.stat-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
}

.stat-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.stat-label {
    font-weight: 700;
    color: #2d3748;
    font-size: 0.95rem;
    min-width: 120px;
    text-transform: none;
    letter-spacing: 0.3px;
}

.stat-value {
    font-weight: 600;
    color: #4a5568;
    font-size: 1rem;
    flex: 1;
    margin-left: 16px;
    text-align: left;
    word-break: break-word;
}

.stat-count {
    font-size: 0.85rem;
    color: #667eea;
    font-weight: 700;
    background: rgba(102, 126, 234, 0.15);
    padding: 6px 12px;
    border-radius: 20px;
    margin-left: 8px;
    white-space: nowrap;
}

/* 现代化加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(248, 250, 252, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: 1.1rem;
    color: #64748b;
    font-weight: 500;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .path-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .path-text {
        margin-right: 0;
        word-break: break-all;
    }
    
    .upload-area {
        padding: 1.5rem;
        min-height: 120px;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
    
    .upload-text {
        font-size: 1rem;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .device-lens-stats {
        gap: 10px;
    }
    
    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px;
    }
    
    .stat-label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .stat-value {
        margin-left: 0;
    }
}

@media (max-width: 768px) {
    .info-cards-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .chart-content {
        height: 280px;
    }
    
    .actions-container {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .empty-state {
        padding: 2.5rem 1.5rem;
    }
    
    .empty-state-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .empty-state-actions .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .charts-section {
        padding: 1.5rem;
        border-radius: 16px;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .chart-content {
        height: 250px;
    }
    
    .info-card {
        padding: 1.25rem;
    }
}
