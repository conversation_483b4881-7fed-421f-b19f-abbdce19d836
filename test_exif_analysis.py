#!/usr/bin/env python3
"""
EXIF数据分析测试脚本
"""

import os
import sys
import traceback

# 添加模块路径
sys.path.append(os.path.dirname(__file__))

from modules.llm_api import EXIFAnalyzer, LLMError

def test_exif_analysis():
    """测试EXIF数据分析功能"""
    
    # CSV文件路径
    csv_path = "data/raw.csv"
    
    if not os.path.exists(csv_path):
        print(f"错误: 找不到CSV文件 {csv_path}")
        return
    
    try:
        # 初始化分析器
        analyzer = EXIFAnalyzer()
        
        # 测试各种分析功能
        analyses = [
            ("focal_length", "焦段分析"),
            ("aperture", "光圈分析"),
            ("shutter_speed", "快门速度分析"),
            ("iso", "ISO分析"),
            ("camera_body", "机身分析"),
            ("lens", "镜头分析"),
            ("user_preference", "用户喜好分析"),
            ("hand_stability", "手持稳定性分析"),
            ("comprehensive", "综合分析")
        ]
        
        for analysis_type, description in analyses:
            print(f"\n{'='*50}")
            print(f"开始 {description}")
            print(f"{'='*50}")
            
            try:
                # 获取分析方法
                method = getattr(analyzer, f"analyze_{analysis_type}")
                
                # 执行分析
                result = method(csv_path)
                
                # 输出结果
                print(f"✓ {description} 完成")
                print(f"结果类型: {type(result).__name__}")
                
                # 输出部分结果
                if hasattr(result, 'dict'):
                    result_dict = result.dict()
                    for key, value in list(result_dict.items())[:3]:
                        print(f"  {key}: {value}")
                        
            except Exception as e:
                print(f"✗ {description} 失败: {str(e)}")
                traceback.print_exc()
                
    except LLMError as e:
        print(f"LLM错误: {str(e)}")
    except Exception as e:
        print(f"未知错误: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_exif_analysis()
