"""
LLM API 配置
实际配置文件，用于测试
"""

# Google Gemini API 配置
GOOGLE_API_KEY = "AIzaSyDdFje213EbmMQTdKneXzj5xw5_Nxk12JA"

# 模型配置
DEFAULT_MODEL = "gemini-2.5-flash"  # 使用最新的模型

# 生成参数配置
GENERATION_CONFIG = {
    "temperature": 0.7,
    "top_p": 0.95,
    "top_k": 40,
    "max_output_tokens": 8192,
}

# 图片处理配置
IMAGE_CONFIG = {
    "max_size": (1024, 1024),
    "quality": 85,
    "format": "JPEG"
}

# 分析配置
ANALYSIS_CONFIG = {
    "default_analysis_type": "basic",
    "enable_batch_analysis": True,
    "max_batch_size": 10,
    "timeout_seconds": 30
}
