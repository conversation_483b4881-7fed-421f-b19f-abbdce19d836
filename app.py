from fastapi import Fast<PERSON><PERSON>, Request, Form, File, UploadFile, HTTPException
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
import os
import json
import pandas as pd
import shutil
import traceback
from pathlib import Path
from typing import List, Optional
from modules.batch_photo_analyzer import BatchPhotoAnalyzer
from modules.basic_analysis import BasicPhotoAnalyzer
from modules.chart_data_processor import PhotoDataProcessor, ChartGenerator
from modules.llm_api import EXIFAnalyzer, LLMError

app = FastAPI(title="照片分析器", description="照片EXIF数据分析工具")

# 静态文件和模板设置
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 配置文件和数据文件路径
config_file = "config.json"
raw_data_file = "./data/raw.csv"
analysis_data_file = "./data/basic_analysis.csv"

def load_config():
    """加载配置文件"""
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {"paths": []}

def save_config(config):
    """保存配置文件"""
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

def ensure_data_directory():
    """确保数据目录存在"""
    os.makedirs("./data", exist_ok=True)

def analyze_all_photos():
    """分析所有配置路径下的照片"""
    config = load_config()
    paths = config.get("paths", [])
    
    if not paths:
        return False, "没有配置照片路径"
    
    ensure_data_directory()
    
    try:
        # 第一步：使用BatchPhotoAnalyzer提取所有照片的原始数据
        batch_analyzer = BatchPhotoAnalyzer()
        
        # 删除旧的原始数据文件，重新生成
        if os.path.exists(raw_data_file):
            os.remove(raw_data_file)
        
        # 处理每个配置的路径
        for path in paths:
            if os.path.exists(path):
                print(f"正在分析路径: {path}")
                batch_analyzer.process_directory(path, raw_data_file)
        
        # 检查是否生成了原始数据
        if not os.path.exists(raw_data_file):
            return False, "未找到任何照片或分析失败"
        
        # 第二步：使用BasicPhotoAnalyzer对原始数据进行统计分析
        basic_analyzer = BasicPhotoAnalyzer()
        analysis_results = basic_analyzer.analyze_photos(raw_data_file)
        
        if not analysis_results:
            return False, "统计分析失败"
        
        # 第三步：将统计结果保存到CSV文件
        basic_analyzer.save_results_to_csv(analysis_results, analysis_data_file)
        
        return True, f"分析完成，共处理了 {analysis_results['总体统计']['总照片数量']} 张照片"
        
    except Exception as e:
        return False, f"分析过程中发生错误: {str(e)}"

def get_chart_data_processor():
    """获取图表数据处理器"""
    processor = PhotoDataProcessor(analysis_data_file)
    if processor.load_and_parse_csv():
        processor.process_data_for_charts()
        return processor
    return None

def get_chart_generator():
    """获取图表生成器"""
    processor = get_chart_data_processor()
    if processor:
        return ChartGenerator(processor)
    return None

def get_chart_data():
    """获取统一的图表数据"""
    processor = get_chart_data_processor()
    if processor:
        return {
            'total_photos': processor.raw_data.get('total_photos', 0),
            'focal_lengths': processor.processed_data.get('focal_lengths', {}),
            'apertures': processor.processed_data.get('apertures', {}),
            'shutter_speeds': processor.processed_data.get('shutter_speeds', {}),
            'iso_values': processor.processed_data.get('iso_values', {}),
            'devices': processor.processed_data.get('devices', {}),
            'lenses': processor.processed_data.get('lenses', {}),
            'months': processor.processed_data.get('months', {}),
            'time_periods': processor.processed_data.get('time_periods', {})
        }
    return None

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    config = load_config()
    
    # 检查是否有分析数据
    chart_data = get_chart_data()
    
    has_data = chart_data is not None and chart_data.get('total_photos', 0) > 0
    total_photos = chart_data.get('total_photos', 0) if chart_data else 0
    
    return templates.TemplateResponse("index.html", {
        "request": request,
        "has_data": has_data,
        "total_photos": total_photos,
        "config": config
    })

@app.get("/api/overview-charts")
async def get_overview_charts():
    """获取主页概览图表数据"""
    try:
        print("📊 正在生成概览图表...")
        generator = get_chart_generator()
        processor = get_chart_data_processor()
        if not generator or not processor:
            print("❌ 没有可用的图表生成器，请先添加照片路径并进行分析")
            raise HTTPException(status_code=404, detail="没有可用的数据，请先添加照片路径并进行分析")
        
        charts = {}
        
        # 月份统计曲线图
        print("🔍 正在生成月份分布曲线图...")
        months_chart = generator.create_line_chart('months', '拍摄月份分布', '月份', '照片数量')
        if months_chart:
            print("✅ 月份分布曲线图生成成功")
            charts['months'] = months_chart
        else:
            print("⚠️ 月份分布曲线图生成失败")
        
        # 时段统计曲线图
        print("🔍 正在生成时段分布曲线图...")
        time_periods_chart = generator.create_line_chart('time_periods', '拍摄时段分布', '时段', '照片数量')
        if time_periods_chart:
            print("✅ 时段分布曲线图生成成功")
            charts['time_periods'] = time_periods_chart
        else:
            print("⚠️ 时段分布曲线图生成失败")
        
        # 获取设备镜头统计信息
        print("🔍 正在获取设备镜头统计信息...")
        device_lens_info = processor.get_top_device_and_lens_info()
        charts['device_lens_info'] = device_lens_info
        print(f"✅ 设备镜头信息获取成功: {device_lens_info}")
        
        print(f"📈 概览图表生成完成，共 {len(charts)} 个图表")
        return JSONResponse(content=charts)
    except Exception as e:
        print(f"❌ 生成概览图表时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"生成图表失败: {str(e)}")

@app.get("/data", response_class=HTMLResponse)
async def data_panel(request: Request):
    """数据面板"""
    return templates.TemplateResponse("data.html", {"request": request})

@app.get("/api/data-charts")
async def get_data_charts():
    """获取数据面板图表"""
    try:
        generator = get_chart_generator()
        if not generator:
            raise HTTPException(status_code=404, detail="没有可用的数据，请先进行分析")
        
        charts = {}
        
        # 焦距柱形图
        focal_chart = generator.create_bar_chart('focal_lengths', '焦距使用统计', 
                                                '焦距范围', '使用次数', sort_by_value=True)
        if focal_chart:
            charts['focal_lengths'] = focal_chart
        
        # 光圈柱形图
        aperture_chart = generator.create_bar_chart('apertures', '光圈使用统计', 
                                                   '光圈范围', '使用次数')
        if aperture_chart:
            charts['apertures'] = aperture_chart
        
        # 快门速度柱形图
        shutter_chart = generator.create_bar_chart('shutter_speeds', '快门速度使用统计', 
                                                  '快门速度范围', '使用次数')
        if shutter_chart:
            charts['shutter_speeds'] = shutter_chart
        
        # ISO柱形图
        iso_chart = generator.create_bar_chart('iso_values', 'ISO使用统计', 
                                              'ISO范围', '使用次数')
        if iso_chart:
            charts['iso_values'] = iso_chart
        
        return JSONResponse(content=charts)
    
    except Exception as e:
        print(f"生成图表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成图表时出错: {str(e)}")

@app.get("/devices", response_class=HTMLResponse)
async def devices_panel(request: Request):
    """设备面板"""
    return templates.TemplateResponse("devices.html", {"request": request})

@app.get("/api/device-charts")
async def get_device_charts():
    """获取设备面板图表"""
    generator = get_chart_generator()
    if not generator:
        raise HTTPException(status_code=404, detail="没有可用的数据，请先进行分析")
    
    charts = {}
    
    # 拍摄设备饼图
    devices_chart = generator.create_pie_chart('devices', '拍摄设备统计')
    if devices_chart:
        charts['devices'] = devices_chart
    
    # 镜头统计饼图
    lenses_chart = generator.create_pie_chart('lenses', '镜头使用统计')
    if lenses_chart:
        charts['lenses'] = lenses_chart
    
    return JSONResponse(content=charts)

@app.get("/settings", response_class=HTMLResponse)
async def settings_panel(request: Request):
    """设置面板"""
    config = load_config()
    return templates.TemplateResponse("settings.html", {
        "request": request,
        "config": config,
        "paths": config.get("paths", [])
    })

@app.post("/api/analyze")
async def analyze_photos():
    """开始分析照片"""
    try:
        success, message = analyze_all_photos()
        
        if success:
            return JSONResponse(content={
                "success": True,
                "message": message
            })
        else:
            raise HTTPException(status_code=400, detail=message)
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@app.post("/api/add-path")
async def add_path(path: str = Form(...)):
    """添加照片路径"""
    try:
        # 路径清理和标准化
        path = path.strip().replace('/', '\\')  # Windows路径标准化
        
        # 基本验证
        if not path:
            raise HTTPException(status_code=400, detail="路径不能为空")
        
        if len(path) < 3:
            raise HTTPException(status_code=400, detail="请输入有效的路径")
        
        # 检查路径是否存在
        if not os.path.exists(path):
            raise HTTPException(status_code=400, detail=f"路径不存在: {path}")
        
        # 检查是否为目录
        if not os.path.isdir(path):
            raise HTTPException(status_code=400, detail=f"指定的路径不是一个文件夹: {path}")
        
        # 检查路径是否可读
        try:
            os.listdir(path)
        except PermissionError:
            raise HTTPException(status_code=400, detail=f"没有访问该路径的权限: {path}")
        
        # 加载配置并添加路径
        config = load_config()
        if path not in config["paths"]:
            config["paths"].append(path)
            save_config(config)
            return JSONResponse(content={
                "success": True, 
                "message": f"路径添加成功: {path}"
            })
        else:
            return JSONResponse(content={
                "success": False, 
                "message": f"路径已存在: {path}"
            })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加路径失败: {str(e)}")

@app.post("/api/remove-path")
async def remove_path(path: str = Form(...)):
    """删除照片路径"""
    try:
        config = load_config()
        if path in config["paths"]:
            config["paths"].remove(path)
            save_config(config)
        
        return JSONResponse(content={"success": True, "message": "路径删除成功"})
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除路径失败: {str(e)}")

@app.get("/api/config")
async def get_config():
    """获取当前配置"""
    config = load_config()
    return JSONResponse(content=config)

@app.get("/api/data-info")
async def get_data_info():
    """获取数据概况信息"""
    chart_data = get_chart_data()
    
    if not chart_data:
        return JSONResponse(content={
            "has_data": False,
            "total_photos": 0,
            "message": "没有可用的数据，请先添加照片路径并进行分析"
        })
    
    return JSONResponse(content={
        "has_data": True,
        "total_photos": chart_data.get('total_photos', 0),
        "data_files": {
            "raw_data_exists": os.path.exists(raw_data_file),
            "analysis_data_exists": os.path.exists(analysis_data_file)
        }
    })

@app.post("/api/upload-photos")
async def upload_photos(photos: List[UploadFile] = File(...)):
    """上传照片并分析"""
    try:
        if not photos:
            raise HTTPException(status_code=400, detail="没有选择照片文件")
        
        ensure_data_directory()
        
        # 创建临时目录存储上传的照片
        upload_dir = "./data/uploads"
        os.makedirs(upload_dir, exist_ok=True)
        
        # 保存上传的照片
        saved_files = []
        folder_structure = {}  # 记录文件夹结构
        
        for photo in photos:
            # 验证文件类型
            if not photo.content_type or not photo.content_type.startswith('image/'):
                print(f"跳过非图片文件: {photo.filename}")
                continue
                
            # 验证文件扩展名
            file_extension = Path(photo.filename).suffix.lower()
            if file_extension not in ['.jpg', '.jpeg', '.png', '.tiff', '.tif']:
                print(f"跳过不支持的格式: {photo.filename}")
                continue
            
            # 处理文件路径，保持文件夹结构
            filename = photo.filename
            if '/' in filename:  # 如果是从文件夹上传的，保持目录结构
                # 创建子目录
                file_dir = os.path.dirname(filename)
                full_dir = os.path.join(upload_dir, file_dir)
                os.makedirs(full_dir, exist_ok=True)
                
                file_path = os.path.join(upload_dir, filename)
                folder_name = filename.split('/')[0]
                
                if folder_name not in folder_structure:
                    folder_structure[folder_name] = []
                folder_structure[folder_name].append(filename)
            else:
                file_path = os.path.join(upload_dir, filename)
            
            # 保存文件
            try:
                with open(file_path, "wb") as buffer:
                    content = await photo.read()
                    buffer.write(content)
                saved_files.append(file_path)
                print(f"保存文件: {filename}")
            except Exception as e:
                print(f"保存文件失败 {filename}: {str(e)}")
                continue
        
        if not saved_files:
            raise HTTPException(status_code=400, detail="没有有效的照片文件")
        
        print(f"共保存 {len(saved_files)} 个文件")
        if folder_structure:
            print(f"文件夹结构: {list(folder_structure.keys())}")
        
        # 使用BatchPhotoAnalyzer分析上传的照片
        batch_analyzer = BatchPhotoAnalyzer()
        
        # 分析每个上传的文件
        analyzed_count = 0
        for file_path in saved_files:
            try:
                # 分析单个文件并追加到raw.csv
                if batch_analyzer.process_single_file(file_path, raw_data_file):
                    analyzed_count += 1
            except Exception as e:
                print(f"分析文件 {file_path} 失败: {str(e)}")
                continue
        
        # 清理临时文件和目录
        import shutil
        try:
            shutil.rmtree(upload_dir)
            print("清理临时文件完成")
        except Exception as e:
            print(f"清理临时文件失败: {str(e)}")
        
        if analyzed_count == 0:
            raise HTTPException(status_code=400, detail="没有成功分析任何照片")
        
        # 重新生成统计数据
        if os.path.exists(raw_data_file):
            basic_analyzer = BasicPhotoAnalyzer()
            analysis_results = basic_analyzer.analyze_photos(raw_data_file)
            
            if analysis_results:
                basic_analyzer.save_results_to_csv(analysis_results, analysis_data_file)
        
        # 生成结果消息
        message = f"成功上传并分析了 {analyzed_count} 张照片"
        if folder_structure:
            folder_info = ", ".join([f"{folder}({len(files)}张)" for folder, files in folder_structure.items()])
            message += f"，来自文件夹: {folder_info}"
        
        return JSONResponse(content={
            "success": True,
            "message": message
        })
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"上传处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传处理失败: {str(e)}")

# EXIF分析相关路由
@app.get("/exif-analysis", response_class=HTMLResponse)
async def exif_analysis_page(request: Request):
    """EXIF分析页面"""
    # 检查是否有数据
    has_data = os.path.exists(raw_data_file)
    total_photos = 0
    
    if has_data:
        try:
            df = pd.read_csv(raw_data_file)
            total_photos = len(df)
        except Exception as e:
            print(f"读取数据文件失败: {str(e)}")
            has_data = False
    
    return templates.TemplateResponse("exif_analysis.html", {
        "request": request,
        "has_data": has_data,
        "total_photos": total_photos
    })

@app.post("/api/exif-analysis")
async def perform_exif_analysis(
    analysis_type: str = Form(...),
    custom_instruction: str = Form("")
):
    """执行EXIF分析"""
    try:
        if not os.path.exists(raw_data_file):
            raise HTTPException(status_code=404, detail="找不到分析数据，请先添加照片路径并进行分析")
        
        # 初始化分析器
        analyzer = EXIFAnalyzer()
        
        # 执行分析
        result = None
        if analysis_type == "focal_length":
            result = analyzer.analyze_focal_length(raw_data_file, custom_instruction)
        elif analysis_type == "aperture":
            result = analyzer.analyze_aperture(raw_data_file, custom_instruction)
        elif analysis_type == "shutter_speed":
            result = analyzer.analyze_shutter_speed(raw_data_file, custom_instruction)
        elif analysis_type == "iso":
            result = analyzer.analyze_iso(raw_data_file, custom_instruction)
        elif analysis_type == "camera_body":
            result = analyzer.analyze_camera_body(raw_data_file, custom_instruction)
        elif analysis_type == "lens":
            result = analyzer.analyze_lens(raw_data_file, custom_instruction)
        elif analysis_type == "user_preference":
            result = analyzer.analyze_user_preference(raw_data_file, custom_instruction)
        elif analysis_type == "hand_stability":
            result = analyzer.analyze_hand_stability(raw_data_file, custom_instruction)
        elif analysis_type == "comprehensive":
            result = analyzer.analyze_comprehensive(raw_data_file, custom_instruction)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的分析类型: {analysis_type}")
        
        # 转换结果为字典
        if result:
            result_dict = result.dict() if hasattr(result, 'dict') else result
            return JSONResponse(content={
                "success": True,
                "analysis_type": analysis_type,
                "result": result_dict
            })
        else:
            raise HTTPException(status_code=500, detail="分析失败，未获得结果")
            
    except LLMError as e:
        print(f"LLM分析错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"LLM分析错误: {str(e)}")
    except Exception as e:
        print(f"EXIF分析失败: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@app.get("/api/exif-analysis-batch")
async def batch_exif_analysis():
    """批量执行所有EXIF分析"""
    try:
        if not os.path.exists(raw_data_file):
            raise HTTPException(status_code=404, detail="找不到分析数据，请先添加照片路径并进行分析")
        
        # 初始化分析器
        analyzer = EXIFAnalyzer()
        
        # 执行所有分析
        results = {}
        analysis_types = [
            ("focal_length", "焦段分析"),
            ("aperture", "光圈分析"),
            ("shutter_speed", "快门速度分析"),
            ("iso", "ISO分析"),
            ("camera_body", "机身分析"),
            ("lens", "镜头分析"),
            ("user_preference", "用户喜好分析"),
            ("hand_stability", "手持稳定性分析"),
            ("comprehensive", "综合分析")
        ]
        
        for analysis_type, description in analysis_types:
            try:
                method = getattr(analyzer, f"analyze_{analysis_type}")
                result = method(raw_data_file)
                results[analysis_type] = {
                    "success": True,
                    "description": description,
                    "result": result.dict() if hasattr(result, 'dict') else result
                }
            except Exception as e:
                results[analysis_type] = {
                    "success": False,
                    "description": description,
                    "error": str(e)
                }
        
        return JSONResponse(content={
            "success": True,
            "results": results
        })
        
    except Exception as e:
        print(f"批量EXIF分析失败: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"批量分析失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
