#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片基础分析器
读取raw.csv文件，按指定区间统计照片数量
"""

import pandas as pd
import csv
import re
import os
from collections import defaultdict


class BasicPhotoAnalyzer:
    """基础照片分析器"""
    
    def __init__(self):
        # 定义各种区间
        self.focal_length_intervals = [
            ("<= 24mm (超广角)", 0, 24),
            ("25mm - 35mm (广角)", 25, 35),
            ("36mm - 60mm (标准/中等广角)", 36, 60),
            ("61mm - 105mm (中焦/中远摄)", 61, 105),
            ("106mm - 200mm (短长焦)", 106, 200),
            ("201mm - 400mm (中长焦)", 201, 400),
            ("401mm - 600mm (超长焦)", 401, 600),
            (">= 601mm (极致超长焦)", 601, float('inf'))
        ]
        
        self.aperture_intervals = [
            ("f/1.6 或更大", 0, 1.6),
            ("f/1.8 - f/2.8", 1.8, 2.8),
            ("f/3.2 - f/4.0", 3.2, 4.0),
            ("f/4.5 - f/6.3", 4.5, 6.3),
            ("f/7.1 - f/9.0", 7.1, 9.0),
            ("f/10 - f/14", 10, 14),
            ("f/16 - f/22", 16, 22),
            ("> f/22", 22.001, float('inf'))
        ]
        
        self.shutter_speed_intervals = [
            ("1/2000s 及更快", 0, 1/2000),
            ("1/500s - 1/1000s", 1/2000.001, 1/500),
            ("1/125s - 1/500s", 1/500.001, 1/125),
            ("1/15s - 1/125s", 1/125.001, 1/15),
            ("1/8s - 1/15s", 1/15.001, 1/8),
            ("> 1/8s (包括长时间曝光)", 1/8.001, float('inf'))
        ]
        
        self.iso_intervals = [
            ("ISO 100 - ISO 400", 100, 400),
            ("ISO 800 - ISO 1600", 800, 1600),
            ("ISO 3200 - ISO 6400", 3200, 6400),
            ("> ISO 6400", 6400.001, float('inf'))
        ]
    
    def parse_aperture(self, aperture_str):
        """解析光圈值"""
        try:
            if pd.isna(aperture_str) or aperture_str == '':
                return None
            
            # 移除 f/ 前缀并转换为浮点数
            if aperture_str.startswith('f/'):
                return float(aperture_str[2:])
            return float(aperture_str)
        except:
            return None
    
    def parse_shutter_speed(self, shutter_str):
        """解析快门速度"""
        try:
            if pd.isna(shutter_str) or shutter_str == '':
                return None
            
            # 移除末尾的 's'
            if shutter_str.endswith('s'):
                shutter_str = shutter_str[:-1]
            
            # 处理分数形式 1/xxx
            if '/' in shutter_str:
                parts = shutter_str.split('/')
                return float(parts[0]) / float(parts[1])
            else:
                return float(shutter_str)
        except:
            return None
    
    def parse_focal_length(self, focal_str):
        """解析焦距值"""
        try:
            if pd.isna(focal_str) or focal_str == '':
                return None
            
            # 移除单位 mm
            if isinstance(focal_str, str) and focal_str.endswith('mm'):
                focal_str = focal_str[:-2]
            
            return float(focal_str)
        except:
            return None
    
    def parse_iso(self, iso_str):
        """解析ISO值"""
        try:
            if pd.isna(iso_str) or iso_str == '':
                return None
            return int(float(iso_str))
        except:
            return None
    
    def categorize_value(self, value, intervals):
        """将值分类到对应区间"""
        if value is None:
            return "未知"
        
        for interval_name, min_val, max_val in intervals:
            if min_val <= value <= max_val:
                return interval_name
        
        return "其他"
    
    def parse_date_time(self, date_str, time_str):
        """解析日期和时间"""
        try:
            if pd.isna(date_str) or date_str == '' or pd.isna(time_str) or time_str == '':
                return None, None
            
            # 解析日期获取月份
            date_parts = date_str.split('-')
            if len(date_parts) >= 2:
                month = int(date_parts[1])
                if month == 1:
                    month_name = "1月"
                elif month == 2:
                    month_name = "2月"
                elif month == 3:
                    month_name = "3月"
                elif month == 4:
                    month_name = "4月"
                elif month == 5:
                    month_name = "5月"
                elif month == 6:
                    month_name = "6月"
                elif month == 7:
                    month_name = "7月"
                elif month == 8:
                    month_name = "8月"
                elif month == 9:
                    month_name = "9月"
                elif month == 10:
                    month_name = "10月"
                elif month == 11:
                    month_name = "11月"
                elif month == 12:
                    month_name = "12月"
                else:
                    month_name = "未知月份"
            else:
                month_name = "未知月份"
            
            # 解析时间获取时间段
            time_parts = time_str.split(':')
            if len(time_parts) >= 1:
                hour = int(time_parts[0])
                if 5 <= hour < 8:
                    time_period = "早晨 (05:00-07:59)"
                elif 8 <= hour < 12:
                    time_period = "上午 (08:00-11:59)"
                elif 12 <= hour < 14:
                    time_period = "中午 (12:00-13:59)"
                elif 14 <= hour < 18:
                    time_period = "下午 (14:00-17:59)"
                elif 18 <= hour < 21:
                    time_period = "傍晚 (18:00-20:59)"
                elif 21 <= hour < 24:
                    time_period = "夜晚 (21:00-23:59)"
                elif 0 <= hour < 5:
                    time_period = "深夜 (00:00-04:59)"
                else:
                    time_period = "未知时段"
            else:
                time_period = "未知时段"
            
            return month_name, time_period
            
        except:
            return "未知月份", "未知时段"
    
    def clean_text(self, text):
        """清理文本，移除多余的空格和特殊字符"""
        if pd.isna(text) or text == '':
            return "未知"
        
        # 转换为字符串并清理
        text = str(text).strip()
        if text == '' or text.lower() == 'nan':
            return "未知"
        
        return text

    def analyze_photos(self, csv_file_path):
        """分析照片数据"""
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file_path, encoding='utf-8')
            
            print(f"读取到 {len(df)} 张照片的数据")
            
            # 初始化统计结果
            results = {
                '总体统计': {'总照片数量': len(df)},
                '焦距分布': defaultdict(int),
                '光圈分布': defaultdict(int),
                '快门速度分布': defaultdict(int),
                'ISO分布': defaultdict(int),
                '设备分布': defaultdict(int),
                '镜头分布': defaultdict(int),
                '月份分布': defaultdict(int),
                '时间段分布': defaultdict(int)
            }
            
            # 统计每个区间的照片数量
            for _, row in df.iterrows():
                # 焦距分析
                focal_length = self.parse_focal_length(row.get('等效35mm焦距', ''))
                focal_category = self.categorize_value(focal_length, self.focal_length_intervals)
                results['焦距分布'][focal_category] += 1
                
                # 光圈分析
                aperture = self.parse_aperture(row.get('光圈', ''))
                aperture_category = self.categorize_value(aperture, self.aperture_intervals)
                results['光圈分布'][aperture_category] += 1
                
                # 快门速度分析
                shutter_speed = self.parse_shutter_speed(row.get('快门', ''))
                shutter_category = self.categorize_value(shutter_speed, self.shutter_speed_intervals)
                results['快门速度分布'][shutter_category] += 1
                
                # ISO分析
                iso = self.parse_iso(row.get('ISO', ''))
                iso_category = self.categorize_value(iso, self.iso_intervals)
                results['ISO分布'][iso_category] += 1
                
                # 设备分析
                device = self.clean_text(row.get('拍摄设备', ''))
                results['设备分布'][device] += 1
                
                # 镜头分析
                lens = self.clean_text(row.get('镜头', ''))
                results['镜头分布'][lens] += 1
                
                # 日期时间分析
                month_name, time_period = self.parse_date_time(row.get('拍摄日期', ''), row.get('时间', ''))
                results['月份分布'][month_name] += 1
                results['时间段分布'][time_period] += 1
            
            return results
            
        except Exception as e:
            print(f"分析过程中出错: {e}")
            return None
    
    def save_results_to_csv(self, results, output_file):
        """将结果保存到CSV文件"""
        try:
            with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入标题
                writer.writerow(['分析类型', '区间/类别', '照片数量', '占比(%)'])
                
                # 总体统计
                total_photos = results['总体统计']['总照片数量']
                writer.writerow(['总体统计', '总照片数量', total_photos, '100.00'])
                writer.writerow([])  # 空行
                
                # 焦距分布
                writer.writerow(['焦距分布', '', '', ''])
                for interval_name, _, _ in self.focal_length_intervals:
                    count = results['焦距分布'][interval_name]
                    percentage = (count / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', interval_name, count, f'{percentage:.2f}'])
                
                # 处理未知焦距
                unknown_focal = results['焦距分布']['未知']
                if unknown_focal > 0:
                    percentage = (unknown_focal / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', '未知', unknown_focal, f'{percentage:.2f}'])
                
                writer.writerow([])  # 空行
                
                # 光圈分布
                writer.writerow(['光圈分布', '', '', ''])
                for interval_name, _, _ in self.aperture_intervals:
                    count = results['光圈分布'][interval_name]
                    percentage = (count / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', interval_name, count, f'{percentage:.2f}'])
                
                # 处理未知光圈
                unknown_aperture = results['光圈分布']['未知']
                if unknown_aperture > 0:
                    percentage = (unknown_aperture / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', '未知', unknown_aperture, f'{percentage:.2f}'])
                
                writer.writerow([])  # 空行
                
                # 快门速度分布
                writer.writerow(['快门速度分布', '', '', ''])
                for interval_name, _, _ in self.shutter_speed_intervals:
                    count = results['快门速度分布'][interval_name]
                    percentage = (count / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', interval_name, count, f'{percentage:.2f}'])
                
                # 处理未知快门速度
                unknown_shutter = results['快门速度分布']['未知']
                if unknown_shutter > 0:
                    percentage = (unknown_shutter / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', '未知', unknown_shutter, f'{percentage:.2f}'])
                
                writer.writerow([])  # 空行
                
                # ISO分布
                writer.writerow(['ISO分布', '', '', ''])
                for interval_name, _, _ in self.iso_intervals:
                    count = results['ISO分布'][interval_name]
                    percentage = (count / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', interval_name, count, f'{percentage:.2f}'])
                
                # 处理未知ISO
                unknown_iso = results['ISO分布']['未知']
                if unknown_iso > 0:
                    percentage = (unknown_iso / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', '未知', unknown_iso, f'{percentage:.2f}'])
                
                writer.writerow([])  # 空行
                
                # 设备分布
                writer.writerow(['设备分布', '', '', ''])
                # 按照照片数量排序
                device_items = sorted(results['设备分布'].items(), key=lambda x: x[1], reverse=True)
                for device, count in device_items:
                    percentage = (count / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', device, count, f'{percentage:.2f}'])
                
                writer.writerow([])  # 空行
                
                # 镜头分布
                writer.writerow(['镜头分布', '', '', ''])
                # 按照照片数量排序
                lens_items = sorted(results['镜头分布'].items(), key=lambda x: x[1], reverse=True)
                for lens, count in lens_items:
                    percentage = (count / total_photos * 100) if total_photos > 0 else 0
                    writer.writerow(['', lens, count, f'{percentage:.2f}'])
                
                writer.writerow([])  # 空行
                
                # 月份分布
                writer.writerow(['月份分布', '', '', ''])
                month_order = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "未知月份"]
                for month in month_order:
                    count = results['月份分布'][month]
                    if count > 0:
                        percentage = (count / total_photos * 100) if total_photos > 0 else 0
                        writer.writerow(['', month, count, f'{percentage:.2f}'])
                
                writer.writerow([])  # 空行
                
                # 时间段分布
                writer.writerow(['时间段分布', '', '', ''])
                time_order = [
                    "深夜 (00:00-04:59)", "早晨 (05:00-07:59)", "上午 (08:00-11:59)", 
                    "中午 (12:00-13:59)", "下午 (14:00-17:59)", "傍晚 (18:00-20:59)", 
                    "夜晚 (21:00-23:59)", "未知时段"
                ]
                for time_period in time_order:
                    count = results['时间段分布'][time_period]
                    if count > 0:
                        percentage = (count / total_photos * 100) if total_photos > 0 else 0
                        writer.writerow(['', time_period, count, f'{percentage:.2f}'])
            
            print(f"分析结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"保存结果时出错: {e}")
    
    def print_results(self, results):
        """打印分析结果"""
        if not results:
            print("没有可用的分析结果")
            return
        
        print("\n" + "="*60)
        print("照片基础分析报告")
        print("="*60)
        
        # 总体统计
        total_photos = results['总体统计']['总照片数量']
        print(f"\n【总体统计】")
        print(f"总照片数量: {total_photos}")
        
        # 焦距分布
        print(f"\n【焦距分布】")
        print("-" * 40)
        for interval_name, _, _ in self.focal_length_intervals:
            count = results['焦距分布'][interval_name]
            percentage = (count / total_photos * 100) if total_photos > 0 else 0
            print(f"{interval_name:<25}: {count:>3} 张 ({percentage:>5.1f}%)")
        
        unknown_focal = results['焦距分布']['未知']
        if unknown_focal > 0:
            percentage = (unknown_focal / total_photos * 100) if total_photos > 0 else 0
            print(f"{'未知':<25}: {unknown_focal:>3} 张 ({percentage:>5.1f}%)")
        
        # 光圈分布
        print(f"\n【光圈分布】")
        print("-" * 40)
        for interval_name, _, _ in self.aperture_intervals:
            count = results['光圈分布'][interval_name]
            percentage = (count / total_photos * 100) if total_photos > 0 else 0
            print(f"{interval_name:<25}: {count:>3} 张 ({percentage:>5.1f}%)")
        
        unknown_aperture = results['光圈分布']['未知']
        if unknown_aperture > 0:
            percentage = (unknown_aperture / total_photos * 100) if total_photos > 0 else 0
            print(f"{'未知':<25}: {unknown_aperture:>3} 张 ({percentage:>5.1f}%)")
        
        # 快门速度分布
        print(f"\n【快门速度分布】")
        print("-" * 40)
        for interval_name, _, _ in self.shutter_speed_intervals:
            count = results['快门速度分布'][interval_name]
            percentage = (count / total_photos * 100) if total_photos > 0 else 0
            print(f"{interval_name:<25}: {count:>3} 张 ({percentage:>5.1f}%)")
        
        unknown_shutter = results['快门速度分布']['未知']
        if unknown_shutter > 0:
            percentage = (unknown_shutter / total_photos * 100) if total_photos > 0 else 0
            print(f"{'未知':<25}: {unknown_shutter:>3} 张 ({percentage:>5.1f}%)")
        
        # ISO分布
        print(f"\n【ISO分布】")
        print("-" * 40)
        for interval_name, _, _ in self.iso_intervals:
            count = results['ISO分布'][interval_name]
            percentage = (count / total_photos * 100) if total_photos > 0 else 0
            print(f"{interval_name:<25}: {count:>3} 张 ({percentage:>5.1f}%)")
        
        unknown_iso = results['ISO分布']['未知']
        if unknown_iso > 0:
            percentage = (unknown_iso / total_photos * 100) if total_photos > 0 else 0
            print(f"{'未知':<25}: {unknown_iso:>3} 张 ({percentage:>5.1f}%)")
        
        # 设备分布
        print(f"\n【设备分布】")
        print("-" * 40)
        device_items = sorted(results['设备分布'].items(), key=lambda x: x[1], reverse=True)
        for device, count in device_items:
            percentage = (count / total_photos * 100) if total_photos > 0 else 0
            print(f"{device:<25}: {count:>3} 张 ({percentage:>5.1f}%)")
        
        # 镜头分布
        print(f"\n【镜头分布】")
        print("-" * 40)
        lens_items = sorted(results['镜头分布'].items(), key=lambda x: x[1], reverse=True)
        for lens, count in lens_items:
            percentage = (count / total_photos * 100) if total_photos > 0 else 0
            # 截断过长的镜头名称以便显示
            display_lens = lens[:22] + "..." if len(lens) > 25 else lens
            print(f"{display_lens:<25}: {count:>3} 张 ({percentage:>5.1f}%)")
        
        # 月份分布
        print(f"\n【月份分布】")
        print("-" * 40)
        month_order = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月", "未知月份"]
        for month in month_order:
            count = results['月份分布'][month]
            if count > 0:
                percentage = (count / total_photos * 100) if total_photos > 0 else 0
                print(f"{month:<25}: {count:>3} 张 ({percentage:>5.1f}%)")
        
        # 时间段分布
        print(f"\n【时间段分布】")
        print("-" * 40)
        time_order = [
            "深夜 (00:00-04:59)", "早晨 (05:00-07:59)", "上午 (08:00-11:59)", 
            "中午 (12:00-13:59)", "下午 (14:00-17:59)", "傍晚 (18:00-20:59)", 
            "夜晚 (21:00-23:59)", "未知时段"
        ]
        for time_period in time_order:
            count = results['时间段分布'][time_period]
            if count > 0:
                percentage = (count / total_photos * 100) if total_photos > 0 else 0
                print(f"{time_period:<25}: {count:>3} 张 ({percentage:>5.1f}%)")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    analyzer = BasicPhotoAnalyzer()
    
    # 设置文件路径
    raw_csv_path = r'f:\Analyzer\data\raw.csv'
    output_csv_path = r'f:\Analyzer\data\basic_analysis.csv'
    
    # 检查输入文件是否存在
    if not os.path.exists(raw_csv_path):
        print(f"错误: 找不到输入文件 {raw_csv_path}")
        return
    
    print("开始分析照片数据...")
    
    # 执行分析
    results = analyzer.analyze_photos(raw_csv_path)
    
    if results:
        # 打印结果
        analyzer.print_results(results)
        
        # 保存结果到CSV
        analyzer.save_results_to_csv(results, output_csv_path)
    else:
        print("分析失败")


if __name__ == "__main__":
    main()
