#!/usr/bin/env python3
"""
照片分析器 Web 应用启动脚本
服务器模式，无UI，适用于后台运行
"""

import uvicorn
import os
import platform
import sys
from app import app
from modules.utils import Colors

def print_server_info():
    """打印服务器信息"""
    print(f"{Colors.BLUE}{Colors.BOLD}╔════════════════════════════════════════════════╗{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.GREEN}照片分析器服务器模式{Colors.BLUE}                        ║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}╠════════════════════════════════════════════════╣{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.YELLOW}• 访问地址: http://127.0.0.1:8000{Colors.BLUE}           ║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.YELLOW}• 按 Ctrl+C 停止服务器{Colors.BLUE}                      ║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}║ {Colors.YELLOW}• 系统: {platform.system()} {platform.release()}{' ' * (33 - len(platform.system()) - len(platform.release()))}{Colors.BLUE}║{Colors.END}")
    print(f"{Colors.BLUE}{Colors.BOLD}╚════════════════════════════════════════════════╝{Colors.END}")

if __name__ == "__main__":
    # 清屏（跨平台）
    os.system('cls' if os.name == 'nt' else 'clear')
    
    # 打印服务器信息
    print_server_info()
    
    # 确保数据目录存在
    if not os.path.exists('./data'):
        print(f"{Colors.YELLOW}创建数据目录...{Colors.END}")
        os.makedirs('./data', exist_ok=True)
    
    # 启动服务器
    try:
        print(f"\n{Colors.GREEN}服务器启动中...{Colors.END}")
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            reload=False,
            access_log=False
        )
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}服务器已关闭{Colors.END}")
    except Exception as e:
        print(f"\n{Colors.RED}错误: {str(e)}{Colors.END}")
        sys.exit(1)
